{"name": "phpmyadmin/phpmyadmin", "type": "project", "description": "MySQL web administration tool", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "mysql", "web"], "homepage": "https://www.phpmyadmin.net/", "support": {"forum": "https://www.phpmyadmin.net/support/", "issues": "https://github.com/phpmyadmin/phpmyadmin/issues", "wiki": "https://wiki.phpmyadmin.net/", "docs": "https://docs.phpmyadmin.net/", "source": "https://github.com/phpmyadmin/phpmyadmin"}, "license": "GPL-2.0-only", "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "non-feature-branches": ["RELEASE_.*"], "autoload": {"psr-4": {"PhpMyAdmin\\": "libraries/classes", "PhpMyAdmin\\Setup\\": "setup/lib"}}, "autoload-dev": {"psr-4": {"PhpMyAdmin\\Tests\\": "test/classes", "PhpMyAdmin\\Tests\\Selenium\\": "test/selenium/"}}, "repositories": [{"type": "composer", "url": "https://www.phpmyadmin.net"}], "require": {"php": ">=5.5.0", "ext-mysqli": "*", "ext-xml": "*", "ext-pcre": "*", "ext-json": "*", "ext-ctype": "*", "ext-hash": "*", "phpmyadmin/sql-parser": "^4.2.3", "phpmyadmin/motranslator": "^4.0", "phpmyadmin/shapefile": "^2.0", "phpseclib/phpseclib": "^2.0", "google/recaptcha": "^1.1", "psr/container": "^1.0", "twig/twig": "^1.34", "twig/extensions": "~1.5.1", "symfony/expression-language": "^3.2 || ^2.8", "symfony/polyfill-mbstring": "^1.3"}, "conflict": {"phpseclib/phpseclib": "2.0.8", "tecnickcom/tcpdf": "<6.2", "pragmarx/google2fa": "<3.0.1", "bacon/bacon-qr-code": "<1.0", "samyoul/u2f-php-server": "<1.1"}, "suggest": {"ext-openssl": "Cookie encryption", "ext-curl": "Updates checking", "ext-opcache": "Better performance", "ext-zlib": "For gz import and export", "ext-bz2": "For bzip2 import and export", "ext-zip": "For zip import and export", "ext-gd2": "For image transformations", "ext-mbstring": "For best performance", "tecnickcom/tcpdf": "For PDF support", "pragmarx/google2fa": "For 2FA authentication", "bacon/bacon-qr-code": "For 2FA authentication", "samyoul/u2f-php-server": "For FIDO U2F authentication"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^5.7", "codacy/coverage": "^1.3.0", "phpunit/phpunit-selenium": "~1.2 || ^3.0", "squizlabs/php_codesniffer": "^3.0", "tecnickcom/tcpdf": "^6.2", "pragmarx/google2fa": "^3.0", "bacon/bacon-qr-code": "^1.0", "samyoul/u2f-php-server": "^1.1", "phpmyadmin/coding-standard": "^0.3"}, "extra": {"branch-alias": {"dev-master": "4.8.x-dev"}}}