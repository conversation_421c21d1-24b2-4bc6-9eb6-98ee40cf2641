
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Index &#8212; phpMyAdmin 4.8.5 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '4.8.5',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 4.8.5 documentation" href="index.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="#" title="General Index"
             accesskey="I">index</a></li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#Symbols"><strong>Symbols</strong></a>
 | <a href="#A"><strong>A</strong></a>
 | <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#J"><strong>J</strong></a>
 | <a href="#K"><strong>K</strong></a>
 | <a href="#L"><strong>L</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#N"><strong>N</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#Q"><strong>Q</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 | <a href="#V"><strong>V</strong></a>
 | <a href="#W"><strong>W</strong></a>
 | <a href="#X"><strong>X</strong></a>
 | <a href="#Z"><strong>Z</strong></a>
 
</div>
<h2 id="Symbols">Symbols</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_ActionLinksMode"><strong>$cfg[&#39;ActionLinksMode&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-137"><strong>$cfg[&#39;AllowArbitraryServer&#39;]</strong></a>, <a href="setup.html#index-0"><strong>[1]</strong></a>, <a href="setup.html#index-20"><strong>[2]</strong></a>, <a href="config.html#cfg_AllowArbitraryServer"><strong>[3]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AllowThirdPartyFraming"><strong>$cfg[&#39;AllowThirdPartyFraming&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AllowUserDropDatabase"><strong>$cfg[&#39;AllowUserDropDatabase&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-136"><strong>$cfg[&#39;ArbitraryServerRegexp&#39;]</strong></a>, <a href="config.html#cfg_ArbitraryServerRegexp"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-4"><strong>$cfg[&#39;AuthLog&#39;]</strong></a>, <a href="setup.html#index-42"><strong>[1]</strong></a>, <a href="config.html#cfg_AuthLog"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-2"><strong>$cfg[&#39;AuthLogSuccess&#39;]</strong></a>, <a href="config.html#cfg_AuthLogSuccess"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AvailableCharsets"><strong>$cfg[&#39;AvailableCharsets&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-134"><strong>$cfg[&#39;blowfish_secret&#39;]</strong></a>, <a href="config.html#cfg_blowfish_secret"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_BrowseMarkerEnable"><strong>$cfg[&#39;BrowseMarkerEnable&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_BrowseMIME"><strong>$cfg[&#39;BrowseMIME&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_BrowsePointerEnable"><strong>$cfg[&#39;BrowsePointerEnable&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_BZipDump"><strong>$cfg[&#39;BZipDump&#39;]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-41"><strong>$cfg[&#39;CaptchaLoginPrivateKey&#39;]</strong></a>, <a href="config.html#cfg_CaptchaLoginPrivateKey"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-40"><strong>$cfg[&#39;CaptchaLoginPublicKey&#39;]</strong></a>, <a href="config.html#cfg_CaptchaLoginPublicKey"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-147"><strong>$cfg[&#39;CharEditing&#39;]</strong></a>, <a href="config.html#cfg_CharEditing"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CharTextareaCols"><strong>$cfg[&#39;CharTextareaCols&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CharTextareaRows"><strong>$cfg[&#39;CharTextareaRows&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CheckConfigurationPermissions"><strong>$cfg[&#39;CheckConfigurationPermissions&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CodemirrorEnable"><strong>$cfg[&#39;CodemirrorEnable&#39;]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-12"><strong>$cfg[&#39;CompressOnFly&#39;]</strong></a>, <a href="config.html#cfg_CompressOnFly"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Confirm"><strong>$cfg[&#39;Confirm&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_AlwaysExpand"><strong>$cfg[&#39;Console&#39;][&#39;AlwaysExpand&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_CurrentQuery"><strong>$cfg[&#39;Console&#39;][&#39;CurrentQuery&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_DarkTheme"><strong>$cfg[&#39;Console&#39;][&#39;DarkTheme&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_EnterExecutes"><strong>$cfg[&#39;Console&#39;][&#39;EnterExecutes&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_Height"><strong>$cfg[&#39;Console&#39;][&#39;Height&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_Mode"><strong>$cfg[&#39;Console&#39;][&#39;Mode&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_StartHistory"><strong>$cfg[&#39;Console&#39;][&#39;StartHistory&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ConsoleEnterExecutes"><strong>$cfg[&#39;ConsoleEnterExecutes&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CSPAllow"><strong>$cfg[&#39;CSPAllow&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG"><strong>$cfg[&#39;DBG&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG_demo"><strong>$cfg[&#39;DBG&#39;][&#39;demo&#39;]</strong></a>
  </dt>

      
  <dt><a href="two_factor.html#index-0"><strong>$cfg[&#39;DBG&#39;][&#39;simple2fa&#39;]</strong></a>, <a href="config.html#cfg_DBG_simple2fa"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-152"><strong>$cfg[&#39;DBG&#39;][&#39;sql&#39;]</strong></a>, <a href="config.html#cfg_DBG_sql"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG_sqllog"><strong>$cfg[&#39;DBG&#39;][&#39;sqllog&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultConnectionCollation"><strong>$cfg[&#39;DefaultConnectionCollation&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultForeignKeyChecks"><strong>$cfg[&#39;DefaultForeignKeyChecks&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultFunctions"><strong>$cfg[&#39;DefaultFunctions&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultLang"><strong>$cfg[&#39;DefaultLang&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultQueryDatabase"><strong>$cfg[&#39;DefaultQueryDatabase&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultQueryTable"><strong>$cfg[&#39;DefaultQueryTable&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTabDatabase"><strong>$cfg[&#39;DefaultTabDatabase&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTabServer"><strong>$cfg[&#39;DefaultTabServer&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTabTable"><strong>$cfg[&#39;DefaultTabTable&#39;]</strong></a>
  </dt>

      
  <dt><a href="transformations.html#index-0"><strong>$cfg[&#39;DefaultTransformations&#39;]</strong></a>, <a href="config.html#cfg_DefaultTransformations"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Bool2Text"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Bool2Text&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_DateFormat"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;DateFormat&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_External"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;External&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Hex"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Hex&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Inline"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Inline&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_PreApPend"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;PreApPend&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Substring"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Substring&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_TextImageLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextImageLink&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_TextLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextLink&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DisableMultiTableMaintenance"><strong>$cfg[&#39;DisableMultiTableMaintenance&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-130"><strong>$cfg[&#39;DisableShortcutKeys&#39;]</strong></a>, <a href="config.html#cfg_DisableShortcutKeys"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DisplayServersList"><strong>$cfg[&#39;DisplayServersList&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_EnableAutocompleteForTablesAndColumns"><strong>$cfg[&#39;EnableAutocompleteForTablesAndColumns&#39;]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-21"><strong>$cfg[&#39;ExecTimeLimit&#39;]</strong></a>, <a href="config.html#cfg_ExecTimeLimit"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export"><strong>$cfg[&#39;Export&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-143"><strong>$cfg[&#39;Export&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#cfg_Export_charset"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_file_template_database"><strong>$cfg[&#39;Export&#39;][&#39;file_template_database&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_file_template_server"><strong>$cfg[&#39;Export&#39;][&#39;file_template_server&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_file_template_table"><strong>$cfg[&#39;Export&#39;][&#39;file_template_table&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_format"><strong>$cfg[&#39;Export&#39;][&#39;format&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_method"><strong>$cfg[&#39;Export&#39;][&#39;method&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_FilterLanguages"><strong>$cfg[&#39;FilterLanguages&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_FirstLevelNavigationItems"><strong>$cfg[&#39;FirstLevelNavigationItems&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_FontSize"><strong>$cfg[&#39;FontSize&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ForceSSL"><strong>$cfg[&#39;ForceSSL&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-141"><strong>$cfg[&#39;ForeignKeyDropdownOrder&#39;]</strong></a>, <a href="config.html#cfg_ForeignKeyDropdownOrder"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-22"><strong>$cfg[&#39;ForeignKeyMaxLimit&#39;]</strong></a>, <a href="config.html#cfg_ForeignKeyMaxLimit"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_GD2Available"><strong>$cfg[&#39;GD2Available&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_GridEditing"><strong>$cfg[&#39;GridEditing&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_GZipDump"><strong>$cfg[&#39;GZipDump&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_HideStructureActions"><strong>$cfg[&#39;HideStructureActions&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_IconvExtraParams"><strong>$cfg[&#39;IconvExtraParams&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_IgnoreMultiSubmitErrors"><strong>$cfg[&#39;IgnoreMultiSubmitErrors&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Import"><strong>$cfg[&#39;Import&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-144"><strong>$cfg[&#39;Import&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#cfg_Import_charset"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_InitialSlidersState"><strong>$cfg[&#39;InitialSlidersState&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_InsertRows"><strong>$cfg[&#39;InsertRows&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Lang"><strong>$cfg[&#39;Lang&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LimitChars"><strong>$cfg[&#39;LimitChars&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LinkLengthLimit"><strong>$cfg[&#39;LinkLengthLimit&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieDeleteAll"><strong>$cfg[&#39;LoginCookieDeleteAll&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieRecall"><strong>$cfg[&#39;LoginCookieRecall&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieStore"><strong>$cfg[&#39;LoginCookieStore&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-135"><strong>$cfg[&#39;LoginCookieValidity&#39;]</strong></a>, <a href="config.html#cfg_LoginCookieValidity"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieValidityDisableWarning"><strong>$cfg[&#39;LoginCookieValidityDisableWarning&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LongtextDoubleTextarea"><strong>$cfg[&#39;LongtextDoubleTextarea&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxCharactersInDisplayedSQL"><strong>$cfg[&#39;MaxCharactersInDisplayedSQL&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxDbList"><strong>$cfg[&#39;MaxDbList&#39;]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-14"><strong>$cfg[&#39;MaxExactCount&#39;]</strong></a>, <a href="faq.html#index-15"><strong>[1]</strong></a>, <a href="config.html#cfg_MaxExactCount"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxExactCountViews"><strong>$cfg[&#39;MaxExactCountViews&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxNavigationItems"><strong>$cfg[&#39;MaxNavigationItems&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxRows"><strong>$cfg[&#39;MaxRows&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxSizeForInputField"><strong>$cfg[&#39;MaxSizeForInputField&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxTableList"><strong>$cfg[&#39;MaxTableList&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MemoryLimit"><strong>$cfg[&#39;MemoryLimit&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MinSizeForInputField"><strong>$cfg[&#39;MinSizeForInputField&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MysqlMinVersion"><strong>$cfg[&#39;MysqlMinVersion&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NaturalOrder"><strong>$cfg[&#39;NaturalOrder&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationDisplayLogo"><strong>$cfg[&#39;NavigationDisplayLogo&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationDisplayServers"><strong>$cfg[&#39;NavigationDisplayServers&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationLinkWithMainPanel"><strong>$cfg[&#39;NavigationLinkWithMainPanel&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationLogoLink"><strong>$cfg[&#39;NavigationLogoLink&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationLogoLinkWindow"><strong>$cfg[&#39;NavigationLogoLinkWindow&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-138"><strong>$cfg[&#39;NavigationTreeDbSeparator&#39;]</strong></a>, <a href="config.html#cfg_NavigationTreeDbSeparator"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-95"><strong>$cfg[&#39;NavigationTreeDefaultTabTable&#39;]</strong></a>, <a href="config.html#index-98"><strong>[1]</strong></a>, <a href="config.html#cfg_NavigationTreeDefaultTabTable"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDefaultTabTable2"><strong>$cfg[&#39;NavigationTreeDefaultTabTable2&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDisplayDbFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayDbFilterMinimum&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDisplayItemFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayItemFilterMinimum&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeEnableExpansion"><strong>$cfg[&#39;NavigationTreeEnableExpansion&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeEnableGrouping"><strong>$cfg[&#39;NavigationTreeEnableGrouping&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreePointerEnable"><strong>$cfg[&#39;NavigationTreePointerEnable&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowEvents"><strong>$cfg[&#39;NavigationTreeShowEvents&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowFunctions"><strong>$cfg[&#39;NavigationTreeShowFunctions&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowProcedures"><strong>$cfg[&#39;NavigationTreeShowProcedures&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowTables"><strong>$cfg[&#39;NavigationTreeShowTables&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowViews"><strong>$cfg[&#39;NavigationTreeShowViews&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeTableLevel"><strong>$cfg[&#39;NavigationTreeTableLevel&#39;]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-13"><strong>$cfg[&#39;NavigationTreeTableSeparator&#39;]</strong></a>, <a href="config.html#cfg_NavigationTreeTableSeparator"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationWidth"><strong>$cfg[&#39;NavigationWidth&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-99"><strong>$cfg[&#39;NumFavoriteTables&#39;]</strong></a>, <a href="faq.html#index-26"><strong>[1]</strong></a>, <a href="config.html#cfg_NumFavoriteTables"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-94"><strong>$cfg[&#39;NumRecentTables&#39;]</strong></a>, <a href="config.html#cfg_NumRecentTables"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-0"><strong>$cfg[&#39;OBGzip&#39;]</strong></a>, <a href="faq.html#index-3"><strong>[1]</strong></a>, <a href="faq.html#index-8"><strong>[2]</strong></a>, <a href="config.html#cfg_OBGzip"><strong>[3]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Order"><strong>$cfg[&#39;Order&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PDFDefaultPageSize"><strong>$cfg[&#39;PDFDefaultPageSize&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-142"><strong>$cfg[&#39;PDFPageSizes&#39;]</strong></a>, <a href="config.html#cfg_PDFPageSizes"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PersistentConnections"><strong>$cfg[&#39;PersistentConnections&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-133"><strong>$cfg[&#39;PmaAbsoluteUri&#39;]</strong></a>, <a href="faq.html#index-10"><strong>[1]</strong></a>, <a href="faq.html#index-19"><strong>[2]</strong></a>, <a href="faq.html#index-20"><strong>[3]</strong></a>, <a href="faq.html#index-6"><strong>[4]</strong></a>, <a href="faq.html#index-9"><strong>[5]</strong></a>, <a href="setup.html#index-6"><strong>[6]</strong></a>, <a href="config.html#cfg_PmaAbsoluteUri"><strong>[7]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PmaNoRelation_DisableWarning"><strong>$cfg[&#39;PmaNoRelation_DisableWarning&#39;]</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_PropertiesNumColumns"><strong>$cfg[&#39;PropertiesNumColumns&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProtectBinary"><strong>$cfg[&#39;ProtectBinary&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProxyPass"><strong>$cfg[&#39;ProxyPass&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProxyUrl"><strong>$cfg[&#39;ProxyUrl&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProxyUser"><strong>$cfg[&#39;ProxyUser&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-148"><strong>$cfg[&#39;QueryHistoryDB&#39;]</strong></a>, <a href="config.html#index-150"><strong>[1]</strong></a>, <a href="config.html#cfg_QueryHistoryDB"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-151"><strong>$cfg[&#39;QueryHistoryMax&#39;]</strong></a>, <a href="config.html#index-91"><strong>[1]</strong></a>, <a href="config.html#cfg_QueryHistoryMax"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RecodingEngine"><strong>$cfg[&#39;RecodingEngine&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RelationalDisplay"><strong>$cfg[&#39;RelationalDisplay&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-102"><strong>$cfg[&#39;RememberSorting&#39;]</strong></a>, <a href="config.html#cfg_RememberSorting"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RepeatCells"><strong>$cfg[&#39;RepeatCells&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ReservedWordDisableWarning"><strong>$cfg[&#39;ReservedWordDisableWarning&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RetainQueryBox"><strong>$cfg[&#39;RetainQueryBox&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RowActionLinks"><strong>$cfg[&#39;RowActionLinks&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RowActionLinksWithoutUnique"><strong>$cfg[&#39;RowActionLinksWithoutUnique&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RowActionType"><strong>$cfg[&#39;RowActionType&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SaveCellsAtOnce"><strong>$cfg[&#39;SaveCellsAtOnce&#39;]</strong></a>
  </dt>

      
  <dt><a href="import_export.html#index-1"><strong>$cfg[&#39;SaveDir&#39;]</strong></a>, <a href="config.html#cfg_SaveDir"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SendErrorReports"><strong>$cfg[&#39;SendErrorReports&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-131"><strong>$cfg[&#39;ServerDefault&#39;]</strong></a>, <a href="config.html#index-132"><strong>[1]</strong></a>, <a href="config.html#cfg_ServerDefault"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ServerLibraryDifference_DisableWarning"><strong>$cfg[&#39;ServerLibraryDifference_DisableWarning&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-153"><strong>$cfg[&#39;Servers&#39;]</strong></a>, <a href="config.html#index-6"><strong>[1]</strong></a>, <a href="config.html#index-9"><strong>[2]</strong></a>, <a href="setup.html#index-17"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers"><strong>[4]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-145"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;]</strong></a>, <a href="setup.html#index-35"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_AllowDeny_order"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-126"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;]</strong></a>, <a href="config.html#index-146"><strong>[1]</strong></a>, <a href="faq.html#index-18"><strong>[2]</strong></a>, <a href="setup.html#index-36"><strong>[3]</strong></a>, <a href="setup.html#index-38"><strong>[4]</strong></a>, <a href="config.html#cfg_Servers_AllowDeny_rules"><strong>[5]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_AllowNoPassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowNoPassword&#39;]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-39"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowRoot&#39;]</strong></a>, <a href="config.html#cfg_Servers_AllowRoot"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_auth_http_realm"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_http_realm&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-75"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_type&#39;]</strong></a>, <a href="setup.html#index-27"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_auth_type"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-78"><strong>$cfg[&#39;Servers&#39;][$i][&#39;bookmarktable&#39;]</strong></a>, <a href="config.html#cfg_Servers_bookmarktable"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-111"><strong>$cfg[&#39;Servers&#39;][$i][&#39;central_columns&#39;]</strong></a>, <a href="config.html#cfg_Servers_central_columns"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-88"><strong>$cfg[&#39;Servers&#39;][$i][&#39;column_info&#39;]</strong></a>, <a href="config.html#index-89"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_column_info"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_compress"><strong>$cfg[&#39;Servers&#39;][$i][&#39;compress&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_connect_type"><strong>$cfg[&#39;Servers&#39;][$i][&#39;connect_type&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-60"><strong>$cfg[&#39;Servers&#39;][$i][&#39;control_*&#39;]</strong></a>, <a href="config.html#index-61"><strong>[1]</strong></a>, <a href="config.html#index-65"><strong>[2]</strong></a>, <a href="config.html#cfg_Servers_control_*"><strong>[3]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-63"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlhost&#39;]</strong></a>, <a href="config.html#cfg_Servers_controlhost"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-17"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlpass&#39;]</strong></a>, <a href="setup.html#index-14"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_controlpass"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-64"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlport&#39;]</strong></a>, <a href="config.html#cfg_Servers_controlport"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-90"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controluser&#39;]</strong></a>, <a href="faq.html#index-16"><strong>[1]</strong></a>, <a href="setup.html#index-13"><strong>[2]</strong></a>, <a href="setup.html#index-16"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers_controluser"><strong>[4]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-113"><strong>$cfg[&#39;Servers&#39;][$i][&#39;designer_settings&#39;]</strong></a>, <a href="config.html#cfg_Servers_designer_settings"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_DisableIS"><strong>$cfg[&#39;Servers&#39;][$i][&#39;DisableIS&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-117"><strong>$cfg[&#39;Servers&#39;][$i][&#39;export_templates&#39;]</strong></a>, <a href="config.html#cfg_Servers_export_templates"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_extension"><strong>$cfg[&#39;Servers&#39;][$i][&#39;extension&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-101"><strong>$cfg[&#39;Servers&#39;][$i][&#39;favorite&#39;]</strong></a>, <a href="config.html#cfg_Servers_favorite"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_hide_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;hide_db&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-149"><strong>$cfg[&#39;Servers&#39;][$i][&#39;history&#39;]</strong></a>, <a href="config.html#index-93"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_history"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-12"><strong>$cfg[&#39;Servers&#39;][$i][&#39;host&#39;]</strong></a>, <a href="config.html#index-13"><strong>[1]</strong></a>, <a href="config.html#index-14"><strong>[2]</strong></a>, <a href="config.html#index-15"><strong>[3]</strong></a>, <a href="config.html#index-59"><strong>[4]</strong></a>, <a href="config.html#index-7"><strong>[5]</strong></a>, <a href="config.html#index-74"><strong>[6]</strong></a>, <a href="config.html#index-8"><strong>[7]</strong></a>, <a href="setup.html#index-1"><strong>[8]</strong></a>, <a href="config.html#cfg_Servers_host"><strong>[9]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_LogoutURL"><strong>$cfg[&#39;Servers&#39;][$i][&#39;LogoutURL&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>$cfg[&#39;Servers&#39;][$i][&#39;MaxTableUiprefs&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-109"><strong>$cfg[&#39;Servers&#39;][$i][&#39;navigationhiding&#39;]</strong></a>, <a href="config.html#cfg_Servers_navigationhiding"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_nopassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;nopassword&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_only_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;only_db&#39;]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-34"><strong>$cfg[&#39;Servers&#39;][$i][&#39;password&#39;]</strong></a>, <a href="config.html#cfg_Servers_password"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-83"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pdf_pages&#39;]</strong></a>, <a href="config.html#index-86"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_pdf_pages"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-1"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pmadb&#39;]</strong></a>, <a href="config.html#index-100"><strong>[1]</strong></a>, <a href="config.html#index-103"><strong>[2]</strong></a>, <a href="config.html#index-105"><strong>[3]</strong></a>, <a href="config.html#index-108"><strong>[4]</strong></a>, <a href="config.html#index-110"><strong>[5]</strong></a>, <a href="config.html#index-112"><strong>[6]</strong></a>, <a href="config.html#index-114"><strong>[7]</strong></a>, <a href="config.html#index-116"><strong>[8]</strong></a>, <a href="config.html#index-118"><strong>[9]</strong></a>, <a href="config.html#index-120"><strong>[10]</strong></a>, <a href="config.html#index-121"><strong>[11]</strong></a>, <a href="config.html#index-139"><strong>[12]</strong></a>, <a href="config.html#index-62"><strong>[13]</strong></a>, <a href="config.html#index-76"><strong>[14]</strong></a>, <a href="config.html#index-77"><strong>[15]</strong></a>, <a href="config.html#index-79"><strong>[16]</strong></a>, <a href="config.html#index-81"><strong>[17]</strong></a>, <a href="config.html#index-84"><strong>[18]</strong></a>, <a href="config.html#index-87"><strong>[19]</strong></a>, <a href="config.html#index-92"><strong>[20]</strong></a>, <a href="config.html#index-96"><strong>[21]</strong></a>, <a href="config.html#cfg_Servers_pmadb"><strong>[22]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-11"><strong>$cfg[&#39;Servers&#39;][$i][&#39;port&#39;]</strong></a>, <a href="config.html#cfg_Servers_port"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-97"><strong>$cfg[&#39;Servers&#39;][$i][&#39;recent&#39;]</strong></a>, <a href="config.html#cfg_Servers_recent"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-80"><strong>$cfg[&#39;Servers&#39;][$i][&#39;relation&#39;]</strong></a>, <a href="config.html#cfg_Servers_relation"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-115"><strong>$cfg[&#39;Servers&#39;][$i][&#39;savedsearches&#39;]</strong></a>, <a href="config.html#cfg_Servers_savedsearches"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_SessionTimeZone"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SessionTimeZone&#39;]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-23"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonCookieParams&#39;]</strong></a>, <a href="setup.html#index-29"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_SignonCookieParams"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-128"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonScript&#39;]</strong></a>, <a href="config.html#index-129"><strong>[1]</strong></a>, <a href="setup.html#index-24"><strong>[2]</strong></a>, <a href="setup.html#index-26"><strong>[3]</strong></a>, <a href="setup.html#index-30"><strong>[4]</strong></a>, <a href="config.html#cfg_Servers_SignonScript"><strong>[5]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-22"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonSession&#39;]</strong></a>, <a href="setup.html#index-28"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_SignonSession"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-25"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonURL&#39;]</strong></a>, <a href="setup.html#index-31"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_SignonURL"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-10"><strong>$cfg[&#39;Servers&#39;][$i][&#39;socket&#39;]</strong></a>, <a href="faq.html#index-7"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_socket"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-154"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl&#39;]</strong></a>, <a href="config.html#index-22"><strong>[1]</strong></a>, <a href="config.html#index-28"><strong>[2]</strong></a>, <a href="config.html#index-34"><strong>[3]</strong></a>, <a href="config.html#index-40"><strong>[4]</strong></a>, <a href="config.html#index-46"><strong>[5]</strong></a>, <a href="config.html#index-52"><strong>[6]</strong></a>, <a href="config.html#index-66"><strong>[7]</strong></a>, <a href="setup.html#index-44"><strong>[8]</strong></a>, <a href="setup.html#index-50"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl"><strong>[10]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-157"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca&#39;]</strong></a>, <a href="config.html#index-18"><strong>[1]</strong></a>, <a href="config.html#index-24"><strong>[2]</strong></a>, <a href="config.html#index-30"><strong>[3]</strong></a>, <a href="config.html#index-43"><strong>[4]</strong></a>, <a href="config.html#index-49"><strong>[5]</strong></a>, <a href="config.html#index-55"><strong>[6]</strong></a>, <a href="config.html#index-69"><strong>[7]</strong></a>, <a href="setup.html#index-47"><strong>[8]</strong></a>, <a href="setup.html#index-53"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl_ca"><strong>[10]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-19"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca_path&#39;]</strong></a>, <a href="config.html#index-25"><strong>[1]</strong></a>, <a href="config.html#index-31"><strong>[2]</strong></a>, <a href="config.html#index-37"><strong>[3]</strong></a>, <a href="config.html#index-50"><strong>[4]</strong></a>, <a href="config.html#index-56"><strong>[5]</strong></a>, <a href="config.html#index-70"><strong>[6]</strong></a>, <a href="setup.html#index-48"><strong>[7]</strong></a>, <a href="setup.html#index-54"><strong>[8]</strong></a>, <a href="config.html#cfg_Servers_ssl_ca_path"><strong>[9]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-156"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_cert&#39;]</strong></a>, <a href="config.html#index-17"><strong>[1]</strong></a>, <a href="config.html#index-23"><strong>[2]</strong></a>, <a href="config.html#index-36"><strong>[3]</strong></a>, <a href="config.html#index-42"><strong>[4]</strong></a>, <a href="config.html#index-48"><strong>[5]</strong></a>, <a href="config.html#index-54"><strong>[6]</strong></a>, <a href="config.html#index-68"><strong>[7]</strong></a>, <a href="setup.html#index-46"><strong>[8]</strong></a>, <a href="setup.html#index-52"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl_cert"><strong>[10]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-20"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ciphers&#39;]</strong></a>, <a href="config.html#index-26"><strong>[1]</strong></a>, <a href="config.html#index-32"><strong>[2]</strong></a>, <a href="config.html#index-38"><strong>[3]</strong></a>, <a href="config.html#index-44"><strong>[4]</strong></a>, <a href="config.html#index-57"><strong>[5]</strong></a>, <a href="config.html#index-71"><strong>[6]</strong></a>, <a href="setup.html#index-55"><strong>[7]</strong></a>, <a href="config.html#cfg_Servers_ssl_ciphers"><strong>[8]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-155"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_key&#39;]</strong></a>, <a href="config.html#index-16"><strong>[1]</strong></a>, <a href="config.html#index-29"><strong>[2]</strong></a>, <a href="config.html#index-35"><strong>[3]</strong></a>, <a href="config.html#index-41"><strong>[4]</strong></a>, <a href="config.html#index-47"><strong>[5]</strong></a>, <a href="config.html#index-53"><strong>[6]</strong></a>, <a href="config.html#index-67"><strong>[7]</strong></a>, <a href="setup.html#index-45"><strong>[8]</strong></a>, <a href="setup.html#index-51"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl_key"><strong>[10]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-158"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_verify&#39;]</strong></a>, <a href="config.html#index-21"><strong>[1]</strong></a>, <a href="config.html#index-27"><strong>[2]</strong></a>, <a href="config.html#index-33"><strong>[3]</strong></a>, <a href="config.html#index-39"><strong>[4]</strong></a>, <a href="config.html#index-45"><strong>[5]</strong></a>, <a href="config.html#index-51"><strong>[6]</strong></a>, <a href="config.html#index-58"><strong>[7]</strong></a>, <a href="config.html#index-72"><strong>[8]</strong></a>, <a href="setup.html#index-49"><strong>[9]</strong></a>, <a href="setup.html#index-56"><strong>[10]</strong></a>, <a href="config.html#cfg_Servers_ssl_verify"><strong>[11]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-85"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_coords&#39;]</strong></a>, <a href="relations.html#index-0"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_table_coords"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-82"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_info&#39;]</strong></a>, <a href="config.html#cfg_Servers_table_info"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-104"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_uiprefs&#39;]</strong></a>, <a href="config.html#index-123"><strong>[1]</strong></a>, <a href="config.html#index-124"><strong>[2]</strong></a>, <a href="config.html#index-125"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers_table_uiprefs"><strong>[4]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-119"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking&#39;]</strong></a>, <a href="config.html#cfg_Servers_tracking"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_database&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_table&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_view&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_default_statements"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_default_statements&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_version_auto_create&#39;]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-33"><strong>$cfg[&#39;Servers&#39;][$i][&#39;user&#39;]</strong></a>, <a href="config.html#cfg_Servers_user"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-122"><strong>$cfg[&#39;Servers&#39;][$i][&#39;userconfig&#39;]</strong></a>, <a href="config.html#cfg_Servers_userconfig"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-107"><strong>$cfg[&#39;Servers&#39;][$i][&#39;usergroups&#39;]</strong></a>, <a href="privileges.html#index-0"><strong>[1]</strong></a>, <a href="privileges.html#index-1"><strong>[2]</strong></a>, <a href="config.html#cfg_Servers_usergroups"><strong>[3]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-106"><strong>$cfg[&#39;Servers&#39;][$i][&#39;users&#39;]</strong></a>, <a href="config.html#cfg_Servers_users"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-140"><strong>$cfg[&#39;Servers&#39;][$i][&#39;verbose&#39;]</strong></a>, <a href="config.html#index-73"><strong>[1]</strong></a>, <a href="faq.html#index-23"><strong>[2]</strong></a>, <a href="setup.html#index-3"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers_verbose"><strong>[4]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-43"><strong>$cfg[&#39;SessionSavePath&#39;]</strong></a>, <a href="config.html#cfg_SessionSavePath"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowAll"><strong>$cfg[&#39;ShowAll&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowBrowseComments"><strong>$cfg[&#39;ShowBrowseComments&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowChgPassword"><strong>$cfg[&#39;ShowChgPassword&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowColumnComments"><strong>$cfg[&#39;ShowColumnComments&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowCreateDb"><strong>$cfg[&#39;ShowCreateDb&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDatabasesNavigationAsTree"><strong>$cfg[&#39;ShowDatabasesNavigationAsTree&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDbStructureCreation"><strong>$cfg[&#39;ShowDbStructureCreation&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDbStructureLastCheck"><strong>$cfg[&#39;ShowDbStructureLastCheck&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDbStructureLastUpdate"><strong>$cfg[&#39;ShowDbStructureLastUpdate&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowFieldTypesInDataEditView"><strong>$cfg[&#39;ShowFieldTypesInDataEditView&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowFunctionFields"><strong>$cfg[&#39;ShowFunctionFields&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowGitRevision"><strong>$cfg[&#39;ShowGitRevision&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowHint"><strong>$cfg[&#39;ShowHint&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowPhpInfo"><strong>$cfg[&#39;ShowPhpInfo&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowPropertyComments"><strong>$cfg[&#39;ShowPropertyComments&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowServerInfo"><strong>$cfg[&#39;ShowServerInfo&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowSQL"><strong>$cfg[&#39;ShowSQL&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowStats"><strong>$cfg[&#39;ShowStats&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SkipLockedTables"><strong>$cfg[&#39;SkipLockedTables&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_Edit"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Edit&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_Explain"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Explain&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_Refresh"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Refresh&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_ShowAsPHP"><strong>$cfg[&#39;SQLQuery&#39;][&#39;ShowAsPHP&#39;]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-5"><strong>$cfg[&#39;SuhosinDisableWarning&#39;]</strong></a>, <a href="config.html#cfg_SuhosinDisableWarning"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TableNavigationLinksMode"><strong>$cfg[&#39;TableNavigationLinksMode&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TablePrimaryKeyOrder"><strong>$cfg[&#39;TablePrimaryKeyOrder&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TabsMode"><strong>$cfg[&#39;TabsMode&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-3"><strong>$cfg[&#39;TempDir&#39;]</strong></a>, <a href="faq.html#index-1"><strong>[1]</strong></a>, <a href="faq.html#index-25"><strong>[2]</strong></a>, <a href="setup.html#index-37"><strong>[3]</strong></a>, <a href="config.html#cfg_TempDir"><strong>[4]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TextareaAutoSelect"><strong>$cfg[&#39;TextareaAutoSelect&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TextareaCols"><strong>$cfg[&#39;TextareaCols&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TextareaRows"><strong>$cfg[&#39;TextareaRows&#39;]</strong></a>
  </dt>

      
  <dt><a href="themes.html#index-1"><strong>$cfg[&#39;ThemeDefault&#39;]</strong></a>, <a href="config.html#cfg_ThemeDefault"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="themes.html#index-0"><strong>$cfg[&#39;ThemeManager&#39;]</strong></a>, <a href="themes.html#index-2"><strong>[1]</strong></a>, <a href="config.html#cfg_ThemeManager"><strong>[2]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ThemePerServer"><strong>$cfg[&#39;ThemePerServer&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleDatabase"><strong>$cfg[&#39;TitleDatabase&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleDefault"><strong>$cfg[&#39;TitleDefault&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleServer"><strong>$cfg[&#39;TitleServer&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleTable"><strong>$cfg[&#39;TitleTable&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TranslationWarningThreshold"><strong>$cfg[&#39;TranslationWarningThreshold&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-127"><strong>$cfg[&#39;TrustedProxies&#39;]</strong></a>, <a href="config.html#cfg_TrustedProxies"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="faq.html#index-2"><strong>$cfg[&#39;UploadDir&#39;]</strong></a>, <a href="faq.html#index-24"><strong>[1]</strong></a>, <a href="import_export.html#index-0"><strong>[2]</strong></a>, <a href="config.html#cfg_UploadDir"><strong>[3]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_UseDbSearch"><strong>$cfg[&#39;UseDbSearch&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_UserprefsDeveloperTab"><strong>$cfg[&#39;UserprefsDeveloperTab&#39;]</strong></a>
  </dt>

      
  <dt><a href="config.html#index-5"><strong>$cfg[&#39;UserprefsDisallow&#39;]</strong></a>, <a href="config.html#cfg_UserprefsDisallow"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_VersionCheck"><strong>$cfg[&#39;VersionCheck&#39;]</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-12"><strong>$cfg[&#39;ZeroConf&#39;]</strong></a>, <a href="config.html#cfg_ZeroConf"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ZipDump"><strong>$cfg[&#39;ZipDump&#39;]</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-htaccess"><strong>.htaccess</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-acl"><strong>ACL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ActionLinksMode"><strong>ActionLinksMode</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AllowArbitraryServer"><strong>AllowArbitraryServer</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_AllowDeny_order"><strong>AllowDeny, order</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_AllowDeny_rules"><strong>AllowDeny, rules</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_AllowNoPassword"><strong>AllowNoPassword</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_AllowRoot"><strong>AllowRoot</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AllowThirdPartyFraming"><strong>AllowThirdPartyFraming</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AllowUserDropDatabase"><strong>AllowUserDropDatabase</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ArbitraryServerRegexp"><strong>ArbitraryServerRegexp</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_Servers_auth_http_realm"><strong>auth_http_realm</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_auth_type"><strong>auth_type</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-15">Authentication mode</a>
  </dt>

      <dd><dl>
        
  <dt><a href="setup.html#index-32">Config</a>
  </dt>

        
  <dt><a href="setup.html#index-19">Cookie</a>
  </dt>

        
  <dt><a href="setup.html#index-18">HTTP</a>
  </dt>

        
  <dt><a href="setup.html#index-21">Signon</a>
  </dt>

      </dl></dd>
      
  <dt><a href="config.html#cfg_AuthLog"><strong>AuthLog</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AuthLogSuccess"><strong>AuthLogSuccess</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_AvailableCharsets"><strong>AvailableCharsets</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-blowfish"><strong>Blowfish</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_blowfish_secret"><strong>blowfish_secret</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_bookmarktable"><strong>bookmarktable</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_BrowseMarkerEnable"><strong>BrowseMarkerEnable</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_BrowseMIME"><strong>BrowseMIME</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_BrowsePointerEnable"><strong>BrowsePointerEnable</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-browser"><strong>Browser</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-bzip2"><strong>bzip2</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_BZipDump"><strong>BZipDump</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_CaptchaLoginPrivateKey"><strong>CaptchaLoginPrivateKey</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CaptchaLoginPublicKey"><strong>CaptchaLoginPublicKey</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_central_columns"><strong>central_columns</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-cgi"><strong>CGI</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-changelog"><strong>Changelog</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CharEditing"><strong>CharEditing</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CharTextareaCols"><strong>CharTextareaCols</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CharTextareaRows"><strong>CharTextareaRows</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CheckConfigurationPermissions"><strong>CheckConfigurationPermissions</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-client"><strong>Client</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CodemirrorEnable"><strong>CodemirrorEnable</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-column"><strong>column</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_column_info"><strong>column_info</strong></a>
  </dt>

      
  <dt><a href="import_export.html#comment">comment (global variable or constant)</a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_compress"><strong>compress</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_CompressOnFly"><strong>CompressOnFly</strong></a>
  </dt>

      
  <dt>
    Config
  </dt>

      <dd><dl>
        
  <dt><a href="setup.html#index-32">Authentication mode</a>
  </dt>

      </dl></dd>
      
  <dt><a href="config.html#index-0">config.inc.php</a>
  </dt>

      
  <dt>
    configuration option
  </dt>

      <dd><dl>
        
  <dt><a href="config.html#cfg_ActionLinksMode"><strong>$cfg[&#39;ActionLinksMode&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-137"><strong>$cfg[&#39;AllowArbitraryServer&#39;]</strong></a>, <a href="setup.html#index-0"><strong>[1]</strong></a>, <a href="setup.html#index-20"><strong>[2]</strong></a>, <a href="config.html#cfg_AllowArbitraryServer"><strong>[3]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_AllowThirdPartyFraming"><strong>$cfg[&#39;AllowThirdPartyFraming&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_AllowUserDropDatabase"><strong>$cfg[&#39;AllowUserDropDatabase&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-136"><strong>$cfg[&#39;ArbitraryServerRegexp&#39;]</strong></a>, <a href="config.html#cfg_ArbitraryServerRegexp"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-4"><strong>$cfg[&#39;AuthLog&#39;]</strong></a>, <a href="setup.html#index-42"><strong>[1]</strong></a>, <a href="config.html#cfg_AuthLog"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-2"><strong>$cfg[&#39;AuthLogSuccess&#39;]</strong></a>, <a href="config.html#cfg_AuthLogSuccess"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_AvailableCharsets"><strong>$cfg[&#39;AvailableCharsets&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_BZipDump"><strong>$cfg[&#39;BZipDump&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_BrowseMIME"><strong>$cfg[&#39;BrowseMIME&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_BrowseMarkerEnable"><strong>$cfg[&#39;BrowseMarkerEnable&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_BrowsePointerEnable"><strong>$cfg[&#39;BrowsePointerEnable&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_CSPAllow"><strong>$cfg[&#39;CSPAllow&#39;]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-41"><strong>$cfg[&#39;CaptchaLoginPrivateKey&#39;]</strong></a>, <a href="config.html#cfg_CaptchaLoginPrivateKey"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-40"><strong>$cfg[&#39;CaptchaLoginPublicKey&#39;]</strong></a>, <a href="config.html#cfg_CaptchaLoginPublicKey"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-147"><strong>$cfg[&#39;CharEditing&#39;]</strong></a>, <a href="config.html#cfg_CharEditing"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_CharTextareaCols"><strong>$cfg[&#39;CharTextareaCols&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_CharTextareaRows"><strong>$cfg[&#39;CharTextareaRows&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_CheckConfigurationPermissions"><strong>$cfg[&#39;CheckConfigurationPermissions&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_CodemirrorEnable"><strong>$cfg[&#39;CodemirrorEnable&#39;]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-12"><strong>$cfg[&#39;CompressOnFly&#39;]</strong></a>, <a href="config.html#cfg_CompressOnFly"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Confirm"><strong>$cfg[&#39;Confirm&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Console_AlwaysExpand"><strong>$cfg[&#39;Console&#39;][&#39;AlwaysExpand&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Console_CurrentQuery"><strong>$cfg[&#39;Console&#39;][&#39;CurrentQuery&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Console_DarkTheme"><strong>$cfg[&#39;Console&#39;][&#39;DarkTheme&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Console_EnterExecutes"><strong>$cfg[&#39;Console&#39;][&#39;EnterExecutes&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Console_Height"><strong>$cfg[&#39;Console&#39;][&#39;Height&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Console_Mode"><strong>$cfg[&#39;Console&#39;][&#39;Mode&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Console_StartHistory"><strong>$cfg[&#39;Console&#39;][&#39;StartHistory&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ConsoleEnterExecutes"><strong>$cfg[&#39;ConsoleEnterExecutes&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DBG"><strong>$cfg[&#39;DBG&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DBG_demo"><strong>$cfg[&#39;DBG&#39;][&#39;demo&#39;]</strong></a>
  </dt>

        
  <dt><a href="two_factor.html#index-0"><strong>$cfg[&#39;DBG&#39;][&#39;simple2fa&#39;]</strong></a>, <a href="config.html#cfg_DBG_simple2fa"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-152"><strong>$cfg[&#39;DBG&#39;][&#39;sql&#39;]</strong></a>, <a href="config.html#cfg_DBG_sql"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DBG_sqllog"><strong>$cfg[&#39;DBG&#39;][&#39;sqllog&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultConnectionCollation"><strong>$cfg[&#39;DefaultConnectionCollation&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultForeignKeyChecks"><strong>$cfg[&#39;DefaultForeignKeyChecks&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultFunctions"><strong>$cfg[&#39;DefaultFunctions&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultLang"><strong>$cfg[&#39;DefaultLang&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultQueryDatabase"><strong>$cfg[&#39;DefaultQueryDatabase&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultQueryTable"><strong>$cfg[&#39;DefaultQueryTable&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTabDatabase"><strong>$cfg[&#39;DefaultTabDatabase&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTabServer"><strong>$cfg[&#39;DefaultTabServer&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTabTable"><strong>$cfg[&#39;DefaultTabTable&#39;]</strong></a>
  </dt>

        
  <dt><a href="transformations.html#index-0"><strong>$cfg[&#39;DefaultTransformations&#39;]</strong></a>, <a href="config.html#cfg_DefaultTransformations"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_Bool2Text"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Bool2Text&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_DateFormat"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;DateFormat&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_External"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;External&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_Hex"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Hex&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_Inline"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Inline&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_PreApPend"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;PreApPend&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_Substring"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Substring&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_TextImageLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextImageLink&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DefaultTransformations_TextLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextLink&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DisableMultiTableMaintenance"><strong>$cfg[&#39;DisableMultiTableMaintenance&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-130"><strong>$cfg[&#39;DisableShortcutKeys&#39;]</strong></a>, <a href="config.html#cfg_DisableShortcutKeys"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_DisplayServersList"><strong>$cfg[&#39;DisplayServersList&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_EnableAutocompleteForTablesAndColumns"><strong>$cfg[&#39;EnableAutocompleteForTablesAndColumns&#39;]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-21"><strong>$cfg[&#39;ExecTimeLimit&#39;]</strong></a>, <a href="config.html#cfg_ExecTimeLimit"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Export"><strong>$cfg[&#39;Export&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-143"><strong>$cfg[&#39;Export&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#cfg_Export_charset"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Export_file_template_database"><strong>$cfg[&#39;Export&#39;][&#39;file_template_database&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Export_file_template_server"><strong>$cfg[&#39;Export&#39;][&#39;file_template_server&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Export_file_template_table"><strong>$cfg[&#39;Export&#39;][&#39;file_template_table&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Export_format"><strong>$cfg[&#39;Export&#39;][&#39;format&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Export_method"><strong>$cfg[&#39;Export&#39;][&#39;method&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_FilterLanguages"><strong>$cfg[&#39;FilterLanguages&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_FirstLevelNavigationItems"><strong>$cfg[&#39;FirstLevelNavigationItems&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_FontSize"><strong>$cfg[&#39;FontSize&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ForceSSL"><strong>$cfg[&#39;ForceSSL&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-141"><strong>$cfg[&#39;ForeignKeyDropdownOrder&#39;]</strong></a>, <a href="config.html#cfg_ForeignKeyDropdownOrder"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-22"><strong>$cfg[&#39;ForeignKeyMaxLimit&#39;]</strong></a>, <a href="config.html#cfg_ForeignKeyMaxLimit"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_GD2Available"><strong>$cfg[&#39;GD2Available&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_GZipDump"><strong>$cfg[&#39;GZipDump&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_GridEditing"><strong>$cfg[&#39;GridEditing&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_HideStructureActions"><strong>$cfg[&#39;HideStructureActions&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_IconvExtraParams"><strong>$cfg[&#39;IconvExtraParams&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_IgnoreMultiSubmitErrors"><strong>$cfg[&#39;IgnoreMultiSubmitErrors&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Import"><strong>$cfg[&#39;Import&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-144"><strong>$cfg[&#39;Import&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#cfg_Import_charset"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_InitialSlidersState"><strong>$cfg[&#39;InitialSlidersState&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_InsertRows"><strong>$cfg[&#39;InsertRows&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Lang"><strong>$cfg[&#39;Lang&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_LimitChars"><strong>$cfg[&#39;LimitChars&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_LinkLengthLimit"><strong>$cfg[&#39;LinkLengthLimit&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_LoginCookieDeleteAll"><strong>$cfg[&#39;LoginCookieDeleteAll&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_LoginCookieRecall"><strong>$cfg[&#39;LoginCookieRecall&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_LoginCookieStore"><strong>$cfg[&#39;LoginCookieStore&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-135"><strong>$cfg[&#39;LoginCookieValidity&#39;]</strong></a>, <a href="config.html#cfg_LoginCookieValidity"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_LoginCookieValidityDisableWarning"><strong>$cfg[&#39;LoginCookieValidityDisableWarning&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_LongtextDoubleTextarea"><strong>$cfg[&#39;LongtextDoubleTextarea&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MaxCharactersInDisplayedSQL"><strong>$cfg[&#39;MaxCharactersInDisplayedSQL&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MaxDbList"><strong>$cfg[&#39;MaxDbList&#39;]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-14"><strong>$cfg[&#39;MaxExactCount&#39;]</strong></a>, <a href="faq.html#index-15"><strong>[1]</strong></a>, <a href="config.html#cfg_MaxExactCount"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MaxExactCountViews"><strong>$cfg[&#39;MaxExactCountViews&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MaxNavigationItems"><strong>$cfg[&#39;MaxNavigationItems&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MaxRows"><strong>$cfg[&#39;MaxRows&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MaxSizeForInputField"><strong>$cfg[&#39;MaxSizeForInputField&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MaxTableList"><strong>$cfg[&#39;MaxTableList&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MemoryLimit"><strong>$cfg[&#39;MemoryLimit&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MinSizeForInputField"><strong>$cfg[&#39;MinSizeForInputField&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_MysqlMinVersion"><strong>$cfg[&#39;MysqlMinVersion&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NaturalOrder"><strong>$cfg[&#39;NaturalOrder&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationDisplayLogo"><strong>$cfg[&#39;NavigationDisplayLogo&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationDisplayServers"><strong>$cfg[&#39;NavigationDisplayServers&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationLinkWithMainPanel"><strong>$cfg[&#39;NavigationLinkWithMainPanel&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationLogoLink"><strong>$cfg[&#39;NavigationLogoLink&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationLogoLinkWindow"><strong>$cfg[&#39;NavigationLogoLinkWindow&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-138"><strong>$cfg[&#39;NavigationTreeDbSeparator&#39;]</strong></a>, <a href="config.html#cfg_NavigationTreeDbSeparator"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-95"><strong>$cfg[&#39;NavigationTreeDefaultTabTable&#39;]</strong></a>, <a href="config.html#index-98"><strong>[1]</strong></a>, <a href="config.html#cfg_NavigationTreeDefaultTabTable"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeDefaultTabTable2"><strong>$cfg[&#39;NavigationTreeDefaultTabTable2&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeDisplayDbFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayDbFilterMinimum&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeDisplayItemFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayItemFilterMinimum&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeEnableExpansion"><strong>$cfg[&#39;NavigationTreeEnableExpansion&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeEnableGrouping"><strong>$cfg[&#39;NavigationTreeEnableGrouping&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreePointerEnable"><strong>$cfg[&#39;NavigationTreePointerEnable&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeShowEvents"><strong>$cfg[&#39;NavigationTreeShowEvents&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeShowFunctions"><strong>$cfg[&#39;NavigationTreeShowFunctions&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeShowProcedures"><strong>$cfg[&#39;NavigationTreeShowProcedures&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeShowTables"><strong>$cfg[&#39;NavigationTreeShowTables&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeShowViews"><strong>$cfg[&#39;NavigationTreeShowViews&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationTreeTableLevel"><strong>$cfg[&#39;NavigationTreeTableLevel&#39;]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-13"><strong>$cfg[&#39;NavigationTreeTableSeparator&#39;]</strong></a>, <a href="config.html#cfg_NavigationTreeTableSeparator"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_NavigationWidth"><strong>$cfg[&#39;NavigationWidth&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-99"><strong>$cfg[&#39;NumFavoriteTables&#39;]</strong></a>, <a href="faq.html#index-26"><strong>[1]</strong></a>, <a href="config.html#cfg_NumFavoriteTables"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-94"><strong>$cfg[&#39;NumRecentTables&#39;]</strong></a>, <a href="config.html#cfg_NumRecentTables"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-0"><strong>$cfg[&#39;OBGzip&#39;]</strong></a>, <a href="faq.html#index-3"><strong>[1]</strong></a>, <a href="faq.html#index-8"><strong>[2]</strong></a>, <a href="config.html#cfg_OBGzip"><strong>[3]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Order"><strong>$cfg[&#39;Order&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_PDFDefaultPageSize"><strong>$cfg[&#39;PDFDefaultPageSize&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-142"><strong>$cfg[&#39;PDFPageSizes&#39;]</strong></a>, <a href="config.html#cfg_PDFPageSizes"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_PersistentConnections"><strong>$cfg[&#39;PersistentConnections&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-133"><strong>$cfg[&#39;PmaAbsoluteUri&#39;]</strong></a>, <a href="faq.html#index-10"><strong>[1]</strong></a>, <a href="faq.html#index-19"><strong>[2]</strong></a>, <a href="faq.html#index-20"><strong>[3]</strong></a>, <a href="faq.html#index-6"><strong>[4]</strong></a>, <a href="faq.html#index-9"><strong>[5]</strong></a>, <a href="setup.html#index-6"><strong>[6]</strong></a>, <a href="config.html#cfg_PmaAbsoluteUri"><strong>[7]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_PmaNoRelation_DisableWarning"><strong>$cfg[&#39;PmaNoRelation_DisableWarning&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_PropertiesNumColumns"><strong>$cfg[&#39;PropertiesNumColumns&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ProtectBinary"><strong>$cfg[&#39;ProtectBinary&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ProxyPass"><strong>$cfg[&#39;ProxyPass&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ProxyUrl"><strong>$cfg[&#39;ProxyUrl&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ProxyUser"><strong>$cfg[&#39;ProxyUser&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-148"><strong>$cfg[&#39;QueryHistoryDB&#39;]</strong></a>, <a href="config.html#index-150"><strong>[1]</strong></a>, <a href="config.html#cfg_QueryHistoryDB"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-151"><strong>$cfg[&#39;QueryHistoryMax&#39;]</strong></a>, <a href="config.html#index-91"><strong>[1]</strong></a>, <a href="config.html#cfg_QueryHistoryMax"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_RecodingEngine"><strong>$cfg[&#39;RecodingEngine&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_RelationalDisplay"><strong>$cfg[&#39;RelationalDisplay&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-102"><strong>$cfg[&#39;RememberSorting&#39;]</strong></a>, <a href="config.html#cfg_RememberSorting"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_RepeatCells"><strong>$cfg[&#39;RepeatCells&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ReservedWordDisableWarning"><strong>$cfg[&#39;ReservedWordDisableWarning&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_RetainQueryBox"><strong>$cfg[&#39;RetainQueryBox&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_RowActionLinks"><strong>$cfg[&#39;RowActionLinks&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_RowActionLinksWithoutUnique"><strong>$cfg[&#39;RowActionLinksWithoutUnique&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_RowActionType"><strong>$cfg[&#39;RowActionType&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_SQLQuery_Edit"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Edit&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_SQLQuery_Explain"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Explain&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_SQLQuery_Refresh"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Refresh&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_SQLQuery_ShowAsPHP"><strong>$cfg[&#39;SQLQuery&#39;][&#39;ShowAsPHP&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_SaveCellsAtOnce"><strong>$cfg[&#39;SaveCellsAtOnce&#39;]</strong></a>
  </dt>

        
  <dt><a href="import_export.html#index-1"><strong>$cfg[&#39;SaveDir&#39;]</strong></a>, <a href="config.html#cfg_SaveDir"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_SendErrorReports"><strong>$cfg[&#39;SendErrorReports&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-131"><strong>$cfg[&#39;ServerDefault&#39;]</strong></a>, <a href="config.html#index-132"><strong>[1]</strong></a>, <a href="config.html#cfg_ServerDefault"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ServerLibraryDifference_DisableWarning"><strong>$cfg[&#39;ServerLibraryDifference_DisableWarning&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-153"><strong>$cfg[&#39;Servers&#39;]</strong></a>, <a href="config.html#index-6"><strong>[1]</strong></a>, <a href="config.html#index-9"><strong>[2]</strong></a>, <a href="setup.html#index-17"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers"><strong>[4]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-145"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;]</strong></a>, <a href="setup.html#index-35"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_AllowDeny_order"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-126"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;]</strong></a>, <a href="config.html#index-146"><strong>[1]</strong></a>, <a href="faq.html#index-18"><strong>[2]</strong></a>, <a href="setup.html#index-36"><strong>[3]</strong></a>, <a href="setup.html#index-38"><strong>[4]</strong></a>, <a href="config.html#cfg_Servers_AllowDeny_rules"><strong>[5]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_AllowNoPassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowNoPassword&#39;]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-39"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowRoot&#39;]</strong></a>, <a href="config.html#cfg_Servers_AllowRoot"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_DisableIS"><strong>$cfg[&#39;Servers&#39;][$i][&#39;DisableIS&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_LogoutURL"><strong>$cfg[&#39;Servers&#39;][$i][&#39;LogoutURL&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>$cfg[&#39;Servers&#39;][$i][&#39;MaxTableUiprefs&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_SessionTimeZone"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SessionTimeZone&#39;]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-23"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonCookieParams&#39;]</strong></a>, <a href="setup.html#index-29"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_SignonCookieParams"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-128"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonScript&#39;]</strong></a>, <a href="config.html#index-129"><strong>[1]</strong></a>, <a href="setup.html#index-24"><strong>[2]</strong></a>, <a href="setup.html#index-26"><strong>[3]</strong></a>, <a href="setup.html#index-30"><strong>[4]</strong></a>, <a href="config.html#cfg_Servers_SignonScript"><strong>[5]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-22"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonSession&#39;]</strong></a>, <a href="setup.html#index-28"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_SignonSession"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-25"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonURL&#39;]</strong></a>, <a href="setup.html#index-31"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_SignonURL"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_auth_http_realm"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_http_realm&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-75"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_type&#39;]</strong></a>, <a href="setup.html#index-27"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_auth_type"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-78"><strong>$cfg[&#39;Servers&#39;][$i][&#39;bookmarktable&#39;]</strong></a>, <a href="config.html#cfg_Servers_bookmarktable"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-111"><strong>$cfg[&#39;Servers&#39;][$i][&#39;central_columns&#39;]</strong></a>, <a href="config.html#cfg_Servers_central_columns"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-88"><strong>$cfg[&#39;Servers&#39;][$i][&#39;column_info&#39;]</strong></a>, <a href="config.html#index-89"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_column_info"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_compress"><strong>$cfg[&#39;Servers&#39;][$i][&#39;compress&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_connect_type"><strong>$cfg[&#39;Servers&#39;][$i][&#39;connect_type&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-60"><strong>$cfg[&#39;Servers&#39;][$i][&#39;control_*&#39;]</strong></a>, <a href="config.html#index-61"><strong>[1]</strong></a>, <a href="config.html#index-65"><strong>[2]</strong></a>, <a href="config.html#cfg_Servers_control_*"><strong>[3]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-63"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlhost&#39;]</strong></a>, <a href="config.html#cfg_Servers_controlhost"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-17"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlpass&#39;]</strong></a>, <a href="setup.html#index-14"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_controlpass"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-64"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlport&#39;]</strong></a>, <a href="config.html#cfg_Servers_controlport"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-90"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controluser&#39;]</strong></a>, <a href="faq.html#index-16"><strong>[1]</strong></a>, <a href="setup.html#index-13"><strong>[2]</strong></a>, <a href="setup.html#index-16"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers_controluser"><strong>[4]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-113"><strong>$cfg[&#39;Servers&#39;][$i][&#39;designer_settings&#39;]</strong></a>, <a href="config.html#cfg_Servers_designer_settings"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-117"><strong>$cfg[&#39;Servers&#39;][$i][&#39;export_templates&#39;]</strong></a>, <a href="config.html#cfg_Servers_export_templates"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_extension"><strong>$cfg[&#39;Servers&#39;][$i][&#39;extension&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-101"><strong>$cfg[&#39;Servers&#39;][$i][&#39;favorite&#39;]</strong></a>, <a href="config.html#cfg_Servers_favorite"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_hide_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;hide_db&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-149"><strong>$cfg[&#39;Servers&#39;][$i][&#39;history&#39;]</strong></a>, <a href="config.html#index-93"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_history"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-12"><strong>$cfg[&#39;Servers&#39;][$i][&#39;host&#39;]</strong></a>, <a href="config.html#index-13"><strong>[1]</strong></a>, <a href="config.html#index-14"><strong>[2]</strong></a>, <a href="config.html#index-15"><strong>[3]</strong></a>, <a href="config.html#index-59"><strong>[4]</strong></a>, <a href="config.html#index-7"><strong>[5]</strong></a>, <a href="config.html#index-74"><strong>[6]</strong></a>, <a href="config.html#index-8"><strong>[7]</strong></a>, <a href="setup.html#index-1"><strong>[8]</strong></a>, <a href="config.html#cfg_Servers_host"><strong>[9]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-109"><strong>$cfg[&#39;Servers&#39;][$i][&#39;navigationhiding&#39;]</strong></a>, <a href="config.html#cfg_Servers_navigationhiding"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_nopassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;nopassword&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_only_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;only_db&#39;]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-34"><strong>$cfg[&#39;Servers&#39;][$i][&#39;password&#39;]</strong></a>, <a href="config.html#cfg_Servers_password"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-83"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pdf_pages&#39;]</strong></a>, <a href="config.html#index-86"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_pdf_pages"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-1"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pmadb&#39;]</strong></a>, <a href="config.html#index-100"><strong>[1]</strong></a>, <a href="config.html#index-103"><strong>[2]</strong></a>, <a href="config.html#index-105"><strong>[3]</strong></a>, <a href="config.html#index-108"><strong>[4]</strong></a>, <a href="config.html#index-110"><strong>[5]</strong></a>, <a href="config.html#index-112"><strong>[6]</strong></a>, <a href="config.html#index-114"><strong>[7]</strong></a>, <a href="config.html#index-116"><strong>[8]</strong></a>, <a href="config.html#index-118"><strong>[9]</strong></a>, <a href="config.html#index-120"><strong>[10]</strong></a>, <a href="config.html#index-121"><strong>[11]</strong></a>, <a href="config.html#index-139"><strong>[12]</strong></a>, <a href="config.html#index-62"><strong>[13]</strong></a>, <a href="config.html#index-76"><strong>[14]</strong></a>, <a href="config.html#index-77"><strong>[15]</strong></a>, <a href="config.html#index-79"><strong>[16]</strong></a>, <a href="config.html#index-81"><strong>[17]</strong></a>, <a href="config.html#index-84"><strong>[18]</strong></a>, <a href="config.html#index-87"><strong>[19]</strong></a>, <a href="config.html#index-92"><strong>[20]</strong></a>, <a href="config.html#index-96"><strong>[21]</strong></a>, <a href="config.html#cfg_Servers_pmadb"><strong>[22]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-11"><strong>$cfg[&#39;Servers&#39;][$i][&#39;port&#39;]</strong></a>, <a href="config.html#cfg_Servers_port"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-97"><strong>$cfg[&#39;Servers&#39;][$i][&#39;recent&#39;]</strong></a>, <a href="config.html#cfg_Servers_recent"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-80"><strong>$cfg[&#39;Servers&#39;][$i][&#39;relation&#39;]</strong></a>, <a href="config.html#cfg_Servers_relation"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-115"><strong>$cfg[&#39;Servers&#39;][$i][&#39;savedsearches&#39;]</strong></a>, <a href="config.html#cfg_Servers_savedsearches"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-10"><strong>$cfg[&#39;Servers&#39;][$i][&#39;socket&#39;]</strong></a>, <a href="faq.html#index-7"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_socket"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-154"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl&#39;]</strong></a>, <a href="config.html#index-22"><strong>[1]</strong></a>, <a href="config.html#index-28"><strong>[2]</strong></a>, <a href="config.html#index-34"><strong>[3]</strong></a>, <a href="config.html#index-40"><strong>[4]</strong></a>, <a href="config.html#index-46"><strong>[5]</strong></a>, <a href="config.html#index-52"><strong>[6]</strong></a>, <a href="config.html#index-66"><strong>[7]</strong></a>, <a href="setup.html#index-44"><strong>[8]</strong></a>, <a href="setup.html#index-50"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl"><strong>[10]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-157"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca&#39;]</strong></a>, <a href="config.html#index-18"><strong>[1]</strong></a>, <a href="config.html#index-24"><strong>[2]</strong></a>, <a href="config.html#index-30"><strong>[3]</strong></a>, <a href="config.html#index-43"><strong>[4]</strong></a>, <a href="config.html#index-49"><strong>[5]</strong></a>, <a href="config.html#index-55"><strong>[6]</strong></a>, <a href="config.html#index-69"><strong>[7]</strong></a>, <a href="setup.html#index-47"><strong>[8]</strong></a>, <a href="setup.html#index-53"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl_ca"><strong>[10]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-19"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca_path&#39;]</strong></a>, <a href="config.html#index-25"><strong>[1]</strong></a>, <a href="config.html#index-31"><strong>[2]</strong></a>, <a href="config.html#index-37"><strong>[3]</strong></a>, <a href="config.html#index-50"><strong>[4]</strong></a>, <a href="config.html#index-56"><strong>[5]</strong></a>, <a href="config.html#index-70"><strong>[6]</strong></a>, <a href="setup.html#index-48"><strong>[7]</strong></a>, <a href="setup.html#index-54"><strong>[8]</strong></a>, <a href="config.html#cfg_Servers_ssl_ca_path"><strong>[9]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-156"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_cert&#39;]</strong></a>, <a href="config.html#index-17"><strong>[1]</strong></a>, <a href="config.html#index-23"><strong>[2]</strong></a>, <a href="config.html#index-36"><strong>[3]</strong></a>, <a href="config.html#index-42"><strong>[4]</strong></a>, <a href="config.html#index-48"><strong>[5]</strong></a>, <a href="config.html#index-54"><strong>[6]</strong></a>, <a href="config.html#index-68"><strong>[7]</strong></a>, <a href="setup.html#index-46"><strong>[8]</strong></a>, <a href="setup.html#index-52"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl_cert"><strong>[10]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-20"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ciphers&#39;]</strong></a>, <a href="config.html#index-26"><strong>[1]</strong></a>, <a href="config.html#index-32"><strong>[2]</strong></a>, <a href="config.html#index-38"><strong>[3]</strong></a>, <a href="config.html#index-44"><strong>[4]</strong></a>, <a href="config.html#index-57"><strong>[5]</strong></a>, <a href="config.html#index-71"><strong>[6]</strong></a>, <a href="setup.html#index-55"><strong>[7]</strong></a>, <a href="config.html#cfg_Servers_ssl_ciphers"><strong>[8]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-155"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_key&#39;]</strong></a>, <a href="config.html#index-16"><strong>[1]</strong></a>, <a href="config.html#index-29"><strong>[2]</strong></a>, <a href="config.html#index-35"><strong>[3]</strong></a>, <a href="config.html#index-41"><strong>[4]</strong></a>, <a href="config.html#index-47"><strong>[5]</strong></a>, <a href="config.html#index-53"><strong>[6]</strong></a>, <a href="config.html#index-67"><strong>[7]</strong></a>, <a href="setup.html#index-45"><strong>[8]</strong></a>, <a href="setup.html#index-51"><strong>[9]</strong></a>, <a href="config.html#cfg_Servers_ssl_key"><strong>[10]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-158"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_verify&#39;]</strong></a>, <a href="config.html#index-21"><strong>[1]</strong></a>, <a href="config.html#index-27"><strong>[2]</strong></a>, <a href="config.html#index-33"><strong>[3]</strong></a>, <a href="config.html#index-39"><strong>[4]</strong></a>, <a href="config.html#index-45"><strong>[5]</strong></a>, <a href="config.html#index-51"><strong>[6]</strong></a>, <a href="config.html#index-58"><strong>[7]</strong></a>, <a href="config.html#index-72"><strong>[8]</strong></a>, <a href="setup.html#index-49"><strong>[9]</strong></a>, <a href="setup.html#index-56"><strong>[10]</strong></a>, <a href="config.html#cfg_Servers_ssl_verify"><strong>[11]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-85"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_coords&#39;]</strong></a>, <a href="relations.html#index-0"><strong>[1]</strong></a>, <a href="config.html#cfg_Servers_table_coords"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-82"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_info&#39;]</strong></a>, <a href="config.html#cfg_Servers_table_info"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-104"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_uiprefs&#39;]</strong></a>, <a href="config.html#index-123"><strong>[1]</strong></a>, <a href="config.html#index-124"><strong>[2]</strong></a>, <a href="config.html#index-125"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers_table_uiprefs"><strong>[4]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-119"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking&#39;]</strong></a>, <a href="config.html#cfg_Servers_tracking"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_database&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_table&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_view&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_default_statements"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_default_statements&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_version_auto_create&#39;]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-33"><strong>$cfg[&#39;Servers&#39;][$i][&#39;user&#39;]</strong></a>, <a href="config.html#cfg_Servers_user"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-122"><strong>$cfg[&#39;Servers&#39;][$i][&#39;userconfig&#39;]</strong></a>, <a href="config.html#cfg_Servers_userconfig"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-107"><strong>$cfg[&#39;Servers&#39;][$i][&#39;usergroups&#39;]</strong></a>, <a href="privileges.html#index-0"><strong>[1]</strong></a>, <a href="privileges.html#index-1"><strong>[2]</strong></a>, <a href="config.html#cfg_Servers_usergroups"><strong>[3]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-106"><strong>$cfg[&#39;Servers&#39;][$i][&#39;users&#39;]</strong></a>, <a href="config.html#cfg_Servers_users"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-140"><strong>$cfg[&#39;Servers&#39;][$i][&#39;verbose&#39;]</strong></a>, <a href="config.html#index-73"><strong>[1]</strong></a>, <a href="faq.html#index-23"><strong>[2]</strong></a>, <a href="setup.html#index-3"><strong>[3]</strong></a>, <a href="config.html#cfg_Servers_verbose"><strong>[4]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-43"><strong>$cfg[&#39;SessionSavePath&#39;]</strong></a>, <a href="config.html#cfg_SessionSavePath"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowAll"><strong>$cfg[&#39;ShowAll&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowBrowseComments"><strong>$cfg[&#39;ShowBrowseComments&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowChgPassword"><strong>$cfg[&#39;ShowChgPassword&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowColumnComments"><strong>$cfg[&#39;ShowColumnComments&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowCreateDb"><strong>$cfg[&#39;ShowCreateDb&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowDatabasesNavigationAsTree"><strong>$cfg[&#39;ShowDatabasesNavigationAsTree&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowDbStructureCreation"><strong>$cfg[&#39;ShowDbStructureCreation&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowDbStructureLastCheck"><strong>$cfg[&#39;ShowDbStructureLastCheck&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowDbStructureLastUpdate"><strong>$cfg[&#39;ShowDbStructureLastUpdate&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowFieldTypesInDataEditView"><strong>$cfg[&#39;ShowFieldTypesInDataEditView&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowFunctionFields"><strong>$cfg[&#39;ShowFunctionFields&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowGitRevision"><strong>$cfg[&#39;ShowGitRevision&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowHint"><strong>$cfg[&#39;ShowHint&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowPhpInfo"><strong>$cfg[&#39;ShowPhpInfo&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowPropertyComments"><strong>$cfg[&#39;ShowPropertyComments&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowSQL"><strong>$cfg[&#39;ShowSQL&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowServerInfo"><strong>$cfg[&#39;ShowServerInfo&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ShowStats"><strong>$cfg[&#39;ShowStats&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_SkipLockedTables"><strong>$cfg[&#39;SkipLockedTables&#39;]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-5"><strong>$cfg[&#39;SuhosinDisableWarning&#39;]</strong></a>, <a href="config.html#cfg_SuhosinDisableWarning"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TableNavigationLinksMode"><strong>$cfg[&#39;TableNavigationLinksMode&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TablePrimaryKeyOrder"><strong>$cfg[&#39;TablePrimaryKeyOrder&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TabsMode"><strong>$cfg[&#39;TabsMode&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-3"><strong>$cfg[&#39;TempDir&#39;]</strong></a>, <a href="faq.html#index-1"><strong>[1]</strong></a>, <a href="faq.html#index-25"><strong>[2]</strong></a>, <a href="setup.html#index-37"><strong>[3]</strong></a>, <a href="config.html#cfg_TempDir"><strong>[4]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TextareaAutoSelect"><strong>$cfg[&#39;TextareaAutoSelect&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TextareaCols"><strong>$cfg[&#39;TextareaCols&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TextareaRows"><strong>$cfg[&#39;TextareaRows&#39;]</strong></a>
  </dt>

        
  <dt><a href="themes.html#index-1"><strong>$cfg[&#39;ThemeDefault&#39;]</strong></a>, <a href="config.html#cfg_ThemeDefault"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="themes.html#index-0"><strong>$cfg[&#39;ThemeManager&#39;]</strong></a>, <a href="themes.html#index-2"><strong>[1]</strong></a>, <a href="config.html#cfg_ThemeManager"><strong>[2]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ThemePerServer"><strong>$cfg[&#39;ThemePerServer&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TitleDatabase"><strong>$cfg[&#39;TitleDatabase&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TitleDefault"><strong>$cfg[&#39;TitleDefault&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TitleServer"><strong>$cfg[&#39;TitleServer&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TitleTable"><strong>$cfg[&#39;TitleTable&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_TranslationWarningThreshold"><strong>$cfg[&#39;TranslationWarningThreshold&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-127"><strong>$cfg[&#39;TrustedProxies&#39;]</strong></a>, <a href="config.html#cfg_TrustedProxies"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="faq.html#index-2"><strong>$cfg[&#39;UploadDir&#39;]</strong></a>, <a href="faq.html#index-24"><strong>[1]</strong></a>, <a href="import_export.html#index-0"><strong>[2]</strong></a>, <a href="config.html#cfg_UploadDir"><strong>[3]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_UseDbSearch"><strong>$cfg[&#39;UseDbSearch&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_UserprefsDeveloperTab"><strong>$cfg[&#39;UserprefsDeveloperTab&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-5"><strong>$cfg[&#39;UserprefsDisallow&#39;]</strong></a>, <a href="config.html#cfg_UserprefsDisallow"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_VersionCheck"><strong>$cfg[&#39;VersionCheck&#39;]</strong></a>
  </dt>

        
  <dt><a href="setup.html#index-12"><strong>$cfg[&#39;ZeroConf&#39;]</strong></a>, <a href="config.html#cfg_ZeroConf"><strong>[1]</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_ZipDump"><strong>$cfg[&#39;ZipDump&#39;]</strong></a>
  </dt>

        
  <dt><a href="config.html#index-134"><strong>$cfg[&#39;blowfish_secret&#39;]</strong></a>, <a href="config.html#cfg_blowfish_secret"><strong>[1]</strong></a>
  </dt>

      </dl></dd>
  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="setup.html#index-11">Configuration storage</a>
  </dt>

      
  <dt><a href="config.html#cfg_Confirm"><strong>Confirm</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_connect_type"><strong>connect_type</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_AlwaysExpand"><strong>Console, AlwaysExpand</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_CurrentQuery"><strong>Console, CurrentQuery</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_DarkTheme"><strong>Console, DarkTheme</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_EnterExecutes"><strong>Console, EnterExecutes</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_Height"><strong>Console, Height</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_Mode"><strong>Console, Mode</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Console_StartHistory"><strong>Console, StartHistory</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ConsoleEnterExecutes"><strong>ConsoleEnterExecutes</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_control_*"><strong>control_*</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_controlhost"><strong>controlhost</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_controlpass"><strong>controlpass</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_controlport"><strong>controlport</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_controluser"><strong>controluser</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-cookie"><strong>Cookie</strong></a>
  </dt>

      <dd><dl>
        
  <dt><a href="setup.html#index-19">Authentication mode</a>
  </dt>

      </dl></dd>
      
  <dt><a href="config.html#cfg_CSPAllow"><strong>CSPAllow</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-csv"><strong>CSV</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="import_export.html#data">data (global variable or constant)</a>
  </dt>

      
  <dt><a href="glossary.html#term-database"><strong>database</strong></a>
  </dt>

      <dd><dl>
        
  <dt><a href="import_export.html#database">(global variable or constant)</a>
  </dt>

      </dl></dd>
      
  <dt><a href="glossary.html#term-db"><strong>DB</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG"><strong>DBG</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG_demo"><strong>DBG, demo</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG_simple2fa"><strong>DBG, simple2fa</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG_sql"><strong>DBG, sql</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DBG_sqllog"><strong>DBG, sqllog</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultConnectionCollation"><strong>DefaultConnectionCollation</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultForeignKeyChecks"><strong>DefaultForeignKeyChecks</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultFunctions"><strong>DefaultFunctions</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultLang"><strong>DefaultLang</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultQueryDatabase"><strong>DefaultQueryDatabase</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultQueryTable"><strong>DefaultQueryTable</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTabDatabase"><strong>DefaultTabDatabase</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_DefaultTabServer"><strong>DefaultTabServer</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTabTable"><strong>DefaultTabTable</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations"><strong>DefaultTransformations</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Bool2Text"><strong>DefaultTransformations, Bool2Text</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_DateFormat"><strong>DefaultTransformations, DateFormat</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_External"><strong>DefaultTransformations, External</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Hex"><strong>DefaultTransformations, Hex</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Inline"><strong>DefaultTransformations, Inline</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_PreApPend"><strong>DefaultTransformations, PreApPend</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_Substring"><strong>DefaultTransformations, Substring</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_TextImageLink"><strong>DefaultTransformations, TextImageLink</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DefaultTransformations_TextLink"><strong>DefaultTransformations, TextLink</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_designer_settings"><strong>designer_settings</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_DisableIS"><strong>DisableIS</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DisableMultiTableMaintenance"><strong>DisableMultiTableMaintenance</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DisableShortcutKeys"><strong>DisableShortcutKeys</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_DisplayServersList"><strong>DisplayServersList</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_EnableAutocompleteForTablesAndColumns"><strong>EnableAutocompleteForTablesAndColumns</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-engine"><strong>Engine</strong></a>
  </dt>

      
  <dt>
    environment variable
  </dt>

      <dd><dl>
        
  <dt><a href="setup.html#envvar-PMA_ABSOLUTE_URI">PMA_ABSOLUTE_URI</a>, <a href="setup.html#index-9">[1]</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_ARBITRARY">PMA_ARBITRARY</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_HOST">PMA_HOST</a>, <a href="setup.html#index-2">[1]</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_HOSTS">PMA_HOSTS</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_PASSWORD">PMA_PASSWORD</a>, <a href="setup.html#index-8">[1]</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_PORT">PMA_PORT</a>, <a href="setup.html#index-5">[1]</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_PORTS">PMA_PORTS</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_USER">PMA_USER</a>, <a href="setup.html#index-7">[1]</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_VERBOSE">PMA_VERBOSE</a>, <a href="setup.html#index-4">[1]</a>
  </dt>

        
  <dt><a href="setup.html#envvar-PMA_VERBOSES">PMA_VERBOSES</a>
  </dt>

      </dl></dd>
  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_ExecTimeLimit"><strong>ExecTimeLimit</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export"><strong>Export</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_charset"><strong>Export, charset</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_file_template_database"><strong>Export, file_template_database</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_file_template_server"><strong>Export, file_template_server</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_file_template_table"><strong>Export, file_template_table</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_format"><strong>Export, format</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Export_method"><strong>Export, method</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_export_templates"><strong>export_templates</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_extension"><strong>extension</strong></a>, <a href="glossary.html#term-extension"><strong>[1]</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-faq"><strong>FAQ</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_favorite"><strong>favorite</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-field"><strong>Field</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_FilterLanguages"><strong>FilterLanguages</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_FirstLevelNavigationItems"><strong>FirstLevelNavigationItems</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_FontSize"><strong>FontSize</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ForceSSL"><strong>ForceSSL</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-foreign-key"><strong>foreign key</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ForeignKeyDropdownOrder"><strong>ForeignKeyDropdownOrder</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ForeignKeyMaxLimit"><strong>ForeignKeyMaxLimit</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-gd"><strong>GD</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-gd2"><strong>GD2</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_GD2Available"><strong>GD2Available</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_GridEditing"><strong>GridEditing</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-gzip"><strong>gzip</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_GZipDump"><strong>GZipDump</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_Servers_hide_db"><strong>hide_db</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_HideStructureActions"><strong>HideStructureActions</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_history"><strong>history</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_host"><strong>host</strong></a>, <a href="glossary.html#term-host"><strong>[1]</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-hostname"><strong>hostname</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-http"><strong>HTTP</strong></a>
  </dt>

      <dd><dl>
        
  <dt><a href="setup.html#index-18">Authentication mode</a>
  </dt>

      </dl></dd>
      
  <dt><a href="glossary.html#term-https"><strong>https</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_IconvExtraParams"><strong>IconvExtraParams</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-iec"><strong>IEC</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_IgnoreMultiSubmitErrors"><strong>IgnoreMultiSubmitErrors</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-iis"><strong>IIS</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Import"><strong>Import</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Import_charset"><strong>Import, charset</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-index"><strong>Index</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_InitialSlidersState"><strong>InitialSlidersState</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_InsertRows"><strong>InsertRows</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-ip"><strong>IP</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-ip-address"><strong>IP Address</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-ipv6"><strong>IPv6</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-isapi"><strong>ISAPI</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-iso"><strong>ISO</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-isp"><strong>ISP</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="J">J</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-jpeg"><strong>JPEG</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-jpg"><strong>JPG</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="K">K</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-key"><strong>Key</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_Lang"><strong>Lang</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-latex"><strong>LATEX</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LimitChars"><strong>LimitChars</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LinkLengthLimit"><strong>LinkLengthLimit</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieDeleteAll"><strong>LoginCookieDeleteAll</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_LoginCookieRecall"><strong>LoginCookieRecall</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieStore"><strong>LoginCookieStore</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieValidity"><strong>LoginCookieValidity</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LoginCookieValidityDisableWarning"><strong>LoginCookieValidityDisableWarning</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_LogoutURL"><strong>LogoutURL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_LongtextDoubleTextarea"><strong>LongtextDoubleTextarea</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-mac"><strong>Mac</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-mac-os-x"><strong>Mac OS X</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxCharactersInDisplayedSQL"><strong>MaxCharactersInDisplayedSQL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxDbList"><strong>MaxDbList</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxExactCount"><strong>MaxExactCount</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxExactCountViews"><strong>MaxExactCountViews</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxNavigationItems"><strong>MaxNavigationItems</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxRows"><strong>MaxRows</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxSizeForInputField"><strong>MaxSizeForInputField</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MaxTableList"><strong>MaxTableList</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>MaxTableUiprefs</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-mbstring"><strong>mbstring</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-mcrypt"><strong>MCrypt</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-42"><strong>mcrypt</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MemoryLimit"><strong>MemoryLimit</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-mime"><strong>MIME</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MinSizeForInputField"><strong>MinSizeForInputField</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-mod-proxy-fcgi"><strong>mod_proxy_fcgi</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-module"><strong>module</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-mysql"><strong>MySQL</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-48"><strong>mysql</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-mysqli"><strong>mysqli</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_MysqlMinVersion"><strong>MysqlMinVersion</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="N">N</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="import_export.html#name">name (global variable or constant)</a>
  </dt>

      
  <dt><a href="config.html#cfg_NaturalOrder"><strong>NaturalOrder</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationDisplayLogo"><strong>NavigationDisplayLogo</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationDisplayServers"><strong>NavigationDisplayServers</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_navigationhiding"><strong>navigationhiding</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationLinkWithMainPanel"><strong>NavigationLinkWithMainPanel</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationLogoLink"><strong>NavigationLogoLink</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationLogoLinkWindow"><strong>NavigationLogoLinkWindow</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDbSeparator"><strong>NavigationTreeDbSeparator</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDefaultTabTable"><strong>NavigationTreeDefaultTabTable</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDefaultTabTable2"><strong>NavigationTreeDefaultTabTable2</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDisplayDbFilterMinimum"><strong>NavigationTreeDisplayDbFilterMinimum</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeDisplayItemFilterMinimum"><strong>NavigationTreeDisplayItemFilterMinimum</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_NavigationTreeEnableExpansion"><strong>NavigationTreeEnableExpansion</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeEnableGrouping"><strong>NavigationTreeEnableGrouping</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreePointerEnable"><strong>NavigationTreePointerEnable</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowEvents"><strong>NavigationTreeShowEvents</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowFunctions"><strong>NavigationTreeShowFunctions</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowProcedures"><strong>NavigationTreeShowProcedures</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowTables"><strong>NavigationTreeShowTables</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeShowViews"><strong>NavigationTreeShowViews</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeTableLevel"><strong>NavigationTreeTableLevel</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationTreeTableSeparator"><strong>NavigationTreeTableSeparator</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NavigationWidth"><strong>NavigationWidth</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_nopassword"><strong>nopassword</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NumFavoriteTables"><strong>NumFavoriteTables</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_NumRecentTables"><strong>NumRecentTables</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_OBGzip"><strong>OBGzip</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_only_db"><strong>only_db</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-opendocument"><strong>OpenDocument</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Order"><strong>Order</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-os-x"><strong>OS X</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_Servers_password"><strong>password</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-pcre"><strong>PCRE</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-pdf"><strong>PDF</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_pdf_pages"><strong>pdf_pages</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PDFDefaultPageSize"><strong>PDFDefaultPageSize</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PDFPageSizes"><strong>PDFPageSizes</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-pear"><strong>PEAR</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PersistentConnections"><strong>PersistentConnections</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-php"><strong>PHP</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-11">phpMyAdmin configuration storage</a>
  </dt>

      
  <dt><a href="setup.html#index-9">PMA_ABSOLUTE_URI</a>
  </dt>

      
  <dt><a href="setup.html#index-2">PMA_HOST</a>
  </dt>

      
  <dt><a href="setup.html#index-8">PMA_PASSWORD</a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="setup.html#index-5">PMA_PORT</a>
  </dt>

      
  <dt><a href="setup.html#index-7">PMA_USER</a>
  </dt>

      
  <dt><a href="setup.html#index-4">PMA_VERBOSE</a>
  </dt>

      
  <dt><a href="config.html#cfg_PmaAbsoluteUri"><strong>PmaAbsoluteUri</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-11">pmadb</a>, <a href="config.html#cfg_Servers_pmadb"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PmaNoRelation_DisableWarning"><strong>PmaNoRelation_DisableWarning</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_port"><strong>port</strong></a>, <a href="glossary.html#term-port"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-primary-key"><strong>primary key</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_PropertiesNumColumns"><strong>PropertiesNumColumns</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProtectBinary"><strong>ProtectBinary</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProxyPass"><strong>ProxyPass</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProxyUrl"><strong>ProxyUrl</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ProxyUser"><strong>ProxyUser</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_QueryHistoryDB"><strong>QueryHistoryDB</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_QueryHistoryMax"><strong>QueryHistoryMax</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_Servers_recent"><strong>recent</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RecodingEngine"><strong>RecodingEngine</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_relation"><strong>relation</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RelationalDisplay"><strong>RelationalDisplay</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RememberSorting"><strong>RememberSorting</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RepeatCells"><strong>RepeatCells</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ReservedWordDisableWarning"><strong>ReservedWordDisableWarning</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RetainQueryBox"><strong>RetainQueryBox</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-rfc"><strong>RFC</strong></a>
  </dt>

      <dd><dl>
        
  <dt><a href="faq.html#index-11">RFC 1867</a>
  </dt>

        
  <dt><a href="glossary.html#index-0">RFC 1952</a>
  </dt>

        
  <dt><a href="faq.html#index-4">RFC 2616</a>
  </dt>

      </dl></dd>
      
  <dt><a href="glossary.html#term-rfc-1952"><strong>RFC 1952</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-row-record-tuple"><strong>Row (record, tuple)</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RowActionLinks"><strong>RowActionLinks</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RowActionLinksWithoutUnique"><strong>RowActionLinksWithoutUnique</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_RowActionType"><strong>RowActionType</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_SaveCellsAtOnce"><strong>SaveCellsAtOnce</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SaveDir"><strong>SaveDir</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_savedsearches"><strong>savedsearches</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SendErrorReports"><strong>SendErrorReports</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-server"><strong>Server</strong></a>
  </dt>

      
  <dt>
    server configuration
  </dt>

      <dd><dl>
        
  <dt><a href="config.html#cfg_Servers_AllowDeny_order"><strong>AllowDeny, order</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_AllowDeny_rules"><strong>AllowDeny, rules</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_AllowNoPassword"><strong>AllowNoPassword</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_AllowRoot"><strong>AllowRoot</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_DisableIS"><strong>DisableIS</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_LogoutURL"><strong>LogoutURL</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>MaxTableUiprefs</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_SessionTimeZone"><strong>SessionTimeZone</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_SignonCookieParams"><strong>SignonCookieParams</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_SignonScript"><strong>SignonScript</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_SignonSession"><strong>SignonSession</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_SignonURL"><strong>SignonURL</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_auth_http_realm"><strong>auth_http_realm</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_auth_type"><strong>auth_type</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_bookmarktable"><strong>bookmarktable</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_central_columns"><strong>central_columns</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_column_info"><strong>column_info</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_compress"><strong>compress</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_connect_type"><strong>connect_type</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_control_*"><strong>control_*</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_controlhost"><strong>controlhost</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_controlpass"><strong>controlpass</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_controlport"><strong>controlport</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_controluser"><strong>controluser</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_designer_settings"><strong>designer_settings</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_export_templates"><strong>export_templates</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_extension"><strong>extension</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_favorite"><strong>favorite</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_hide_db"><strong>hide_db</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_history"><strong>history</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_host"><strong>host</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_navigationhiding"><strong>navigationhiding</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_nopassword"><strong>nopassword</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_only_db"><strong>only_db</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_password"><strong>password</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_pdf_pages"><strong>pdf_pages</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_pmadb"><strong>pmadb</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_port"><strong>port</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_recent"><strong>recent</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_relation"><strong>relation</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_savedsearches"><strong>savedsearches</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_socket"><strong>socket</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_ssl"><strong>ssl</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_ssl_ca"><strong>ssl_ca</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_ssl_ca_path"><strong>ssl_ca_path</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_ssl_cert"><strong>ssl_cert</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_ssl_ciphers"><strong>ssl_ciphers</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_ssl_key"><strong>ssl_key</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_ssl_verify"><strong>ssl_verify</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_table_coords"><strong>table_coords</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_table_info"><strong>table_info</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_table_uiprefs"><strong>table_uiprefs</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking"><strong>tracking</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>tracking_add_drop_database</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>tracking_add_drop_table</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>tracking_add_drop_view</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_default_statements"><strong>tracking_default_statements</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>tracking_version_auto_create</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_user"><strong>user</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_userconfig"><strong>userconfig</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_usergroups"><strong>usergroups</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_users"><strong>users</strong></a>
  </dt>

        
  <dt><a href="config.html#cfg_Servers_verbose"><strong>verbose</strong></a>
  </dt>

      </dl></dd>
  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_ServerDefault"><strong>ServerDefault</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ServerLibraryDifference_DisableWarning"><strong>ServerLibraryDifference_DisableWarning</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers"><strong>Servers</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SessionSavePath"><strong>SessionSavePath</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_SessionTimeZone"><strong>SessionTimeZone</strong></a>
  </dt>

      
  <dt><a href="setup.html#index-10">Setup script</a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowAll"><strong>ShowAll</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowBrowseComments"><strong>ShowBrowseComments</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowChgPassword"><strong>ShowChgPassword</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowColumnComments"><strong>ShowColumnComments</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowCreateDb"><strong>ShowCreateDb</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDatabasesNavigationAsTree"><strong>ShowDatabasesNavigationAsTree</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDbStructureCreation"><strong>ShowDbStructureCreation</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDbStructureLastCheck"><strong>ShowDbStructureLastCheck</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowDbStructureLastUpdate"><strong>ShowDbStructureLastUpdate</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowFieldTypesInDataEditView"><strong>ShowFieldTypesInDataEditView</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowFunctionFields"><strong>ShowFunctionFields</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowGitRevision"><strong>ShowGitRevision</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowHint"><strong>ShowHint</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowPhpInfo"><strong>ShowPhpInfo</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowPropertyComments"><strong>ShowPropertyComments</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowServerInfo"><strong>ShowServerInfo</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowSQL"><strong>ShowSQL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ShowStats"><strong>ShowStats</strong></a>
  </dt>

      
  <dt>
    Signon
  </dt>

      <dd><dl>
        
  <dt><a href="setup.html#index-21">Authentication mode</a>
  </dt>

      </dl></dd>
      
  <dt><a href="config.html#cfg_Servers_SignonCookieParams"><strong>SignonCookieParams</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_SignonScript"><strong>SignonScript</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_SignonSession"><strong>SignonSession</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_SignonURL"><strong>SignonURL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SkipLockedTables"><strong>SkipLockedTables</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_socket"><strong>socket</strong></a>, <a href="glossary.html#term-socket"><strong>[1]</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-sql"><strong>SQL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_Edit"><strong>SQLQuery, Edit</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_Explain"><strong>SQLQuery, Explain</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_Refresh"><strong>SQLQuery, Refresh</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SQLQuery_ShowAsPHP"><strong>SQLQuery, ShowAsPHP</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-ssl"><strong>SSL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_ssl"><strong>ssl</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_ssl_ca"><strong>ssl_ca</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_ssl_ca_path"><strong>ssl_ca_path</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_ssl_cert"><strong>ssl_cert</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_ssl_ciphers"><strong>ssl_ciphers</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_ssl_key"><strong>ssl_key</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_ssl_verify"><strong>ssl_verify</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-storage-engines"><strong>Storage Engines</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-stored-procedure"><strong>Stored procedure</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_SuhosinDisableWarning"><strong>SuhosinDisableWarning</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-table"><strong>table</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_table_coords"><strong>table_coords</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_table_info"><strong>table_info</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_table_uiprefs"><strong>table_uiprefs</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TableNavigationLinksMode"><strong>TableNavigationLinksMode</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TablePrimaryKeyOrder"><strong>TablePrimaryKeyOrder</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TabsMode"><strong>TabsMode</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-tar"><strong>tar</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-tcp"><strong>TCP</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-tcpdf"><strong>TCPDF</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TempDir"><strong>TempDir</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TextareaAutoSelect"><strong>TextareaAutoSelect</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TextareaCols"><strong>TextareaCols</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TextareaRows"><strong>TextareaRows</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ThemeDefault"><strong>ThemeDefault</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_ThemeManager"><strong>ThemeManager</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_ThemePerServer"><strong>ThemePerServer</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleDatabase"><strong>TitleDatabase</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleDefault"><strong>TitleDefault</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleServer"><strong>TitleServer</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TitleTable"><strong>TitleTable</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking"><strong>tracking</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>tracking_add_drop_database</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>tracking_add_drop_table</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>tracking_add_drop_view</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_default_statements"><strong>tracking_default_statements</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>tracking_version_auto_create</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TranslationWarningThreshold"><strong>TranslationWarningThreshold</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-trigger"><strong>trigger</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_TrustedProxies"><strong>TrustedProxies</strong></a>
  </dt>

      
  <dt><a href="import_export.html#type">type (global variable or constant)</a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-unique-key"><strong>unique key</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_UploadDir"><strong>UploadDir</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-url"><strong>URL</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_UseDbSearch"><strong>UseDbSearch</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_user"><strong>user</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_Servers_userconfig"><strong>userconfig</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_usergroups"><strong>usergroups</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_UserprefsDeveloperTab"><strong>UserprefsDeveloperTab</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_UserprefsDisallow"><strong>UserprefsDisallow</strong></a>
  </dt>

      
  <dt><a href="config.html#cfg_Servers_users"><strong>users</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_Servers_verbose"><strong>verbose</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="import_export.html#version">version (global variable or constant)</a>
  </dt>

      
  <dt><a href="config.html#cfg_VersionCheck"><strong>VersionCheck</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="W">W</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-webserver"><strong>Webserver</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="X">X</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="glossary.html#term-xml"><strong>XML</strong></a>
  </dt>

  </dl></td>
</tr></table>

<h2 id="Z">Z</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_ZeroConf"><strong>ZeroConf</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-zip"><strong>ZIP</strong></a>
  </dt>

  </dl></td>
  <td style="width: 33%" valign="top"><dl>
      
  <dt><a href="config.html#cfg_ZipDump"><strong>ZipDump</strong></a>
  </dt>

      
  <dt><a href="glossary.html#term-zlib"><strong>zlib</strong></a>
  </dt>

  </dl></td>
</tr></table>



          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">

   

<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="#" title="General Index"
             >index</a></li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>