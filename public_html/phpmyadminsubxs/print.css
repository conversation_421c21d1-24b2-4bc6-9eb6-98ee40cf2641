.nowrap {
    white-space: nowrap;
}

.hide {
    display: none;
}

body, table, th, td {
    color:             #000;
    background-color:  #fff;
}

img {
    border: 0;
}

table, th, td {
    border: .1em solid #000;
}

table {
    border-collapse:   collapse;
    border-spacing:    0;
}

th, td {
    padding:           0.2em;
}

th {
    font-weight:       bold;
    background-color:  #e5e5e5;
}

th.vtop, td.vtop {
    vertical-align: top;
}

th.v<PERSON><PERSON>, td.vbottom {
    vertical-align: bottom;
}

@media print {
    .print_ignore {
        display: none;
    }

    .nowrap {
        white-space: nowrap;
    }

    .hide {
        display: none;
    }

    body, table, th, td {
        color:             #000;
        background-color:  #fff;
    }

    img {
        border: 0;
    }

    table, th, td {
        border: .1em solid #000;
    }

    table {
        border-collapse:   collapse;
        border-spacing:    0;
    }

    th, td {
        padding:           0.2em;
    }

    th {
        font-weight:       bold;
        background-color:  #e5e5e5;
    }

    th.vtop, td.vtop {
        vertical-align: top;
    }

    th.v<PERSON><PERSON>, td.v<PERSON><PERSON> {
        vertical-align: bottom;
    }
}

