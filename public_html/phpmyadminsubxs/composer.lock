{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "546f5ce1f7778a51182ee46f0356d063", "packages": [{"name": "bacon/bacon-qr-code", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "5a91b62b9d37cee635bbf8d553f4546057250bee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/5a91b62b9d37cee635bbf8d553f4546057250bee", "reference": "5a91b62b9d37cee635bbf8d553f4546057250bee", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.4|^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "suggest": {"ext-gd": "to generate QR code images"}, "type": "library", "autoload": {"psr-0": {"BaconQrCode": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "http://www.dasprids.de", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "time": "2017-10-17T09:59:25+00:00"}, {"name": "google/recaptcha", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/google/recaptcha.git", "reference": "e7add3be59211482ecdb942288f52da64a35f61a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/recaptcha/zipball/e7add3be59211482ecdb942288f52da64a35f61a", "reference": "e7add3be59211482ecdb942288f52da64a35f61a", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.2.20|^2.12", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^4.8.36|^5.7.27|^6.59|^7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"ReCaptcha\\": "src/ReCaptcha"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Client library for reCAPTCHA, a free service that protects websites from spam and abuse.", "homepage": "https://www.google.com/recaptcha/", "keywords": ["Abuse", "<PERSON><PERSON>a", "recaptcha", "spam"], "time": "2018-08-05T09:31:53+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "2132f0f293d856026d7d11bd81b9f4a23a1dc1f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/2132f0f293d856026d7d11bd81b9f4a23a1dc1f6", "reference": "2132f0f293d856026d7d11bd81b9f4a23a1dc1f6", "shasum": ""}, "require": {"php": "^5.3|^7"}, "require-dev": {"paragonie/random_compat": "^1.4|^2", "phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^0.3|^1"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "time": "2018-04-30T17:57:16+00:00"}, {"name": "paragonie/random_compat", "version": "v2.0.18", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2019-01-03T20:59:08+00:00"}, {"name": "phpmyadmin/motranslator", "version": "4.0", "source": {"type": "git", "url": "https://github.com/phpmyadmin/motranslator.git", "reference": "fcb370254998fda7eeccfd7c787b4deb71b0d77c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpmyadmin/motranslator/zipball/fcb370254998fda7eeccfd7c787b4deb71b0d77c", "reference": "fcb370254998fda7eeccfd7c787b4deb71b0d77c", "shasum": ""}, "require": {"php": ">=5.3.0", "symfony/expression-language": "^4.0 || ^3.2 || ^2.8"}, "require-dev": {"apigen/apigen": "^4.1", "phpunit/php-code-coverage": "*", "phpunit/phpunit": "~4.8 || ~5.7 || ~6.5"}, "type": "library", "autoload": {"psr-4": {"PhpMyAdmin\\MoTranslator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "description": "Translation API for PHP using Gettext MO files", "homepage": "https://github.com/phpmyadmin/motranslator", "keywords": ["gettext", "i18n", "mo", "translator"], "time": "2018-02-12T13:22:52+00:00"}, {"name": "phpmyadmin/shapefile", "version": "2.1", "source": {"type": "git", "url": "https://github.com/phpmyadmin/shapefile.git", "reference": "e23b767f2a81f61fee3fc09fc062879985f3e224"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpmyadmin/shapefile/zipball/e23b767f2a81f61fee3fc09fc062879985f3e224", "reference": "e23b767f2a81f61fee3fc09fc062879985f3e224", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/php-code-coverage": "*", "phpunit/phpunit": "~4.8 || ~5.7"}, "suggest": {"ext-dbase": "For dbf files parsing"}, "type": "library", "autoload": {"psr-4": {"PhpMyAdmin\\ShapeFile\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "description": "ESRI ShapeFile library for PHP", "homepage": "https://github.com/phpmyadmin/shapefile", "keywords": ["ESRI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dbf", "geo", "geospatial", "shape", "shp"], "time": "2017-05-15T08:31:47+00:00"}, {"name": "phpmyadmin/sql-parser", "version": "v4.3.1", "source": {"type": "git", "url": "https://github.com/phpmyadmin/sql-parser.git", "reference": "0eb16ef5e3acacbc792be336754e42d98791a33f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpmyadmin/sql-parser/zipball/0eb16ef5e3acacbc792be336754e42d98791a33f", "reference": "0eb16ef5e3acacbc792be336754e42d98791a33f", "shasum": ""}, "require": {"php": ">=5.3.0", "symfony/polyfill-mbstring": "^1.3"}, "conflict": {"phpmyadmin/motranslator": "<3.0"}, "require-dev": {"phpunit/php-code-coverage": "*", "phpunit/phpunit": "~4.8 || ~5.7 || ~6.5", "sami/sami": "^4.0"}, "suggest": {"ext-mbstring": "For best performance", "phpmyadmin/motranslator": "Translate messages to your favorite locale"}, "bin": ["bin/highlight-query", "bin/lint-query"], "type": "library", "autoload": {"psr-4": {"PhpMyAdmin\\SqlParser\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "description": "A validating SQL lexer and parser with a focus on MySQL dialect.", "homepage": "https://github.com/phpmyadmin/sql-parser", "keywords": ["analysis", "lexer", "parser", "sql"], "time": "2019-01-05T13:46:38+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.13", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "42603ce3f42a27f7e14e54feab95db7b680ad473"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/42603ce3f42a27f7e14e54feab95db7b680ad473", "reference": "42603ce3f42a27f7e14e54feab95db7b680ad473", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "time": "2018-12-16T17:45:25+00:00"}, {"name": "pragmarx/google2fa", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa.git", "reference": "6949226739e4424f40031e6f1c96b1fd64047335"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa/zipball/6949226739e4424f40031e6f1c96b1fd64047335", "reference": "6949226739e4424f40031e6f1c96b1fd64047335", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "~1.0|~2.0", "paragonie/random_compat": ">=1", "php": ">=5.4", "symfony/polyfill-php56": "~1.2"}, "require-dev": {"bacon/bacon-qr-code": "~1.0", "phpunit/phpunit": "~4|~5|~6"}, "suggest": {"bacon/bacon-qr-code": "Required to generate inline QR Codes."}, "type": "library", "extra": {"component": "package", "branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"PragmaRX\\Google2FA\\": "src/", "PragmaRX\\Google2FA\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa", "laravel"], "time": "2018-08-29T13:28:06+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "samyoul/u2f-php-server", "version": "v1.1.4", "source": {"type": "git", "url": "https://github.com/Samyoul/U2F-php-server.git", "reference": "0625202c79d570e58525ed6c4ae38500ea3f0883"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Samyoul/U2F-php-server/zipball/0625202c79d570e58525ed6c4ae38500ea3f0883", "reference": "0625202c79d570e58525ed6c4ae38500ea3f0883", "shasum": ""}, "require": {"ext-openssl": "*"}, "type": "library", "autoload": {"psr-4": {"Samyoul\\U2F\\U2FServer\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>-<PERSON>", "email": "<EMAIL>"}], "description": "Server side handling class for FIDO U2F registration and authentication", "time": "2018-10-26T12:43:39+00:00"}, {"name": "symfony/expression-language", "version": "v2.8.49", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "fa9be1b831859b56d244137fabbfd01a46dbdb36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/fa9be1b831859b56d244137fabbfd01a46dbdb36", "reference": "fa9be1b831859b56d244137fabbfd01a46dbdb36", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ExpressionLanguage Component", "homepage": "https://symfony.com", "time": "2018-11-11T11:18:13+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "e3d826245268269cd66f8326bd8bc066687b4a19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/e3d826245268269cd66f8326bd8bc066687b4a19", "reference": "e3d826245268269cd66f8326bd8bc066687b4a19", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2018-08-06T14:22:27+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "c79c051f5b3a46be09205c73b80b346e4153e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/c79c051f5b3a46be09205c73b80b346e4153e494", "reference": "c79c051f5b3a46be09205c73b80b346e4153e494", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2018-09-21T13:07:52+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "ff208829fe1aa48ab9af356992bb7199fed551af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/ff208829fe1aa48ab9af356992bb7199fed551af", "reference": "ff208829fe1aa48ab9af356992bb7199fed551af", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-util": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php56\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2018-09-21T06:26:08+00:00"}, {"name": "symfony/polyfill-util", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-util.git", "reference": "3b58903eae668d348a7126f999b0da0f2f93611c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-util/zipball/3b58903eae668d348a7126f999b0da0f2f93611c", "reference": "3b58903eae668d348a7126f999b0da0f2f93611c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Util\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony utilities for portability of PHP codes", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "polyfill", "shim"], "time": "2018-09-30T16:36:12+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.2.26", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "367241059ca166e3a76490f4448c284e0a161f15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/367241059ca166e3a76490f4448c284e0a161f15", "reference": "367241059ca166e3a76490f4448c284e0a161f15", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_parser.php", "tcpdf_import.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "time": "2018-10-16T17:24:05+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v1.37.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "66be9366c76cbf23e82e7171d47cbfa54a057a62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/66be9366c76cbf23e82e7171d47cbfa54a057a62", "reference": "66be9366c76cbf23e82e7171d47cbfa54a057a62", "shasum": ""}, "require": {"php": ">=5.4.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/debug": "^2.7", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.37-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}, {"name": "Twig Team", "homepage": "https://twig.symfony.com/contributors", "role": "Contributors"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2019-01-14T14:59:29+00:00"}], "packages-dev": [{"name": "codacy/coverage", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/codacy/php-codacy-coverage.git", "reference": "4988cd098db4d578681bfd3176071931ad475150"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/codacy/php-codacy-coverage/zipball/4988cd098db4d578681bfd3176071931ad475150", "reference": "4988cd098db4d578681bfd3176071931ad475150", "shasum": ""}, "require": {"gitonomy/gitlib": ">=1.0", "php": ">=5.3.3", "symfony/console": "~2.5|~3.0|~4.0"}, "require-dev": {"phpunit/phpunit": "~6.5"}, "bin": ["bin/codacycoverage"], "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Sends PHP test coverage information to Codacy.", "homepage": "https://github.com/codacy/php-codacy-coverage", "time": "2018-03-22T16:43:39+00:00"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14T21:17:01+00:00"}, {"name": "gitonomy/gitlib", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/gitonomy/gitlib.git", "reference": "932a960221ae3484a3e82553b3be478e56beb68d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gitonomy/gitlib/zipball/932a960221ae3484a3e82553b3be478e56beb68d", "reference": "932a960221ae3484a3e82553b3be478e56beb68d", "shasum": ""}, "require": {"php": "^5.3 || ^7.0", "symfony/process": "^2.3|^3.0|^4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.7", "psr/log": "^1.0"}, "suggest": {"psr/log": "Add some log"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Gitonomy\\Git\\": "src/Gitonomy/Git/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://alexandre-salome.fr"}, {"name": "Julien DIDIER", "email": "<EMAIL>", "homepage": "http://www.jdidier.net"}], "description": "Library for accessing git", "homepage": "http://gitonomy.com", "time": "2018-04-22T19:55:36+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "3.2.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "4aada1f93c72c35e22fb1383b47fee43b8f1d157"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/4aada1f93c72c35e22fb1383b47fee43b8f1d157", "reference": "4aada1f93c72c35e22fb1383b47fee43b8f1d157", "shasum": ""}, "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.3.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "type": "library", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2017-08-08T06:39:58+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "fb3933512008d8162b3cdf9e18dba9309b7c3773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/fb3933512008d8162b3cdf9e18dba9309b7c3773", "reference": "fb3933512008d8162b3cdf9e18dba9309b7c3773", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-06-03T08:32:36+00:00"}, {"name": "phpmyadmin/coding-standard", "version": "0.3", "source": {"type": "git", "url": "https://github.com/phpmyadmin/coding-standard.git", "reference": "5ae123e27140a1e16be005432e26a8233524eaf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpmyadmin/coding-standard/zipball/5ae123e27140a1e16be005432e26a8233524eaf5", "reference": "5ae123e27140a1e16be005432e26a8233524eaf5", "shasum": ""}, "require": {"squizlabs/php_codesniffer": "^3.0"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "description": "phpMyAdmin PHP CodeSniffer Coding Standard", "keywords": ["codesniffer", "phpcs", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "time": "2017-09-28T09:13:00+00:00"}, {"name": "phpspec/prophecy", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/4ba436b55987b4bf311cb7c6ba82aa528aac0a06", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0|^3.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2018-08-05T17:53:17+00:00"}, {"name": "phpunit/php-code-coverage", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/eabf68b476ac7d0f73793aada060f1c1a9bf8979", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-text-template": "~1.2", "phpunit/php-token-stream": "~1.3", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0"}, "require-dev": {"ext-xdebug": ">=2.1.4", "phpunit/phpunit": "~4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1", "ext-xmlwriter": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2015-10-06T15:47:00+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.4.12", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/1ce90ba27c42e4e44e6d8458241466380b51fa16", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2017-12-04T08:55:13+00:00"}, {"name": "phpunit/phpunit", "version": "4.8.36", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "46023de9a91eec7dfb06cc56cb4e260017298517"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/46023de9a91eec7dfb06cc56cb4e260017298517", "reference": "46023de9a91eec7dfb06cc56cb4e260017298517", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpspec/prophecy": "^1.3.1", "phpunit/php-code-coverage": "~2.1", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "~2.3", "sebastian/comparator": "~1.2.2", "sebastian/diff": "~1.2", "sebastian/environment": "~1.3", "sebastian/exporter": "~1.2", "sebastian/global-state": "~1.0", "sebastian/version": "~1.0", "symfony/yaml": "~2.1|~3.0"}, "suggest": {"phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.8.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2017-06-21T08:07:12+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "2.3.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/ac8e7a3db35738d56ee9a76e78a4e03d97628983", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": ">=5.3.3", "phpunit/php-text-template": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2015-10-02T06:51:40+00:00"}, {"name": "phpunit/phpunit-selenium", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/giorgiosironi/phpunit-selenium.git", "reference": "c84dd7ca214563868ce216123b7ae9c792beb262"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giorgiosironi/phpunit-selenium/zipball/c84dd7ca214563868ce216123b7ae9c792beb262", "reference": "c84dd7ca214563868ce216123b7ae9c792beb262", "shasum": ""}, "require": {"ext-curl": "*", "ext-dom": "*", "php": ">=5.3.3", "phpunit/phpunit": "~3.7|~4.0", "sebastian/comparator": "~1.0"}, "type": "library", "autoload": {"classmap": ["PHPUnit/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "developer"}], "description": "Selenium Server integration for PHPUnit", "homepage": "http://www.phpunit.de/", "keywords": ["selenium", "testing", "xunit"], "time": "2014-11-02T09:23:27+00:00"}, {"name": "psr/log", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2018-11-20T15:27:04+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-05-22T07:24:03+00:00"}, {"name": "sebastian/environment", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "be2c607e43ce4c89ecd60e75c6a85c126e754aea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/environment/zipball/be2c607e43ce4c89ecd60e75c6a85c126e754aea", "reference": "be2c607e43ce4c89ecd60e75c6a85c126e754aea", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-08-18T05:49:44+00:00"}, {"name": "sebastian/exporter", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/42c4c2eec485ee3e159ec9884f95b431287edde4", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~1.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-06-17T09:04:28+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/recursion-context", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "b19cc3298482a335a95f3016d2f8a6950f0fbcd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/b19cc3298482a335a95f3016d2f8a6950f0fbcd7", "reference": "b19cc3298482a335a95f3016d2f8a6950f0fbcd7", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2016-10-03T07:41:43+00:00"}, {"name": "sebastian/version", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "shasum": ""}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2015-06-21T13:59:46+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "379deb987e26c7cd103a7b387aea178baec96e48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/379deb987e26c7cd103a7b387aea178baec96e48", "reference": "379deb987e26c7cd103a7b387aea178baec96e48", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "http://www.squizlabs.com/php-codesniffer", "keywords": ["phpcs", "standards"], "time": "2018-12-19T23:57:18+00:00"}, {"name": "symfony/console", "version": "v2.8.49", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "cbcf4b5e233af15cd2bbd50dee1ccc9b7927dc12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/cbcf4b5e233af15cd2bbd50dee1ccc9b7927dc12", "reference": "cbcf4b5e233af15cd2bbd50dee1ccc9b7927dc12", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/debug": "^2.7.2|~3.0.0", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/event-dispatcher": "~2.1|~3.0.0", "symfony/process": "~2.1|~3.0.0"}, "suggest": {"psr/log-implementation": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2018-11-20T15:55:20+00:00"}, {"name": "symfony/debug", "version": "v2.8.49", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "74251c8d50dd3be7c4ce0c7b862497cdc641a5d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/74251c8d50dd3be7c4ce0c7b862497cdc641a5d0", "reference": "74251c8d50dd3be7c4ce0c7b862497cdc641a5d0", "shasum": ""}, "require": {"php": ">=5.3.9", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/class-loader": "~2.2|~3.0.0", "symfony/http-kernel": "~2.3.24|~2.5.9|^2.6.2|~3.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2018-11-11T11:18:13+00:00"}, {"name": "symfony/process", "version": "v2.8.49", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "c3591a09c78639822b0b290d44edb69bf9f05dc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/c3591a09c78639822b0b290d44edb69bf9f05dc8", "reference": "c3591a09c78639822b0b290d44edb69bf9f05dc8", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2018-11-11T11:18:13+00:00"}, {"name": "symfony/yaml", "version": "v2.8.49", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "02c1859112aa779d9ab394ae4f3381911d84052b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/02c1859112aa779d9ab394ae4f3381911d84052b", "reference": "02c1859112aa779d9ab394ae4f3381911d84052b", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2018-11-11T11:18:13+00:00"}, {"name": "webmozart/assert", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/83e253c8e0be5b0257b881e1827274667c5c17a9", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2018-12-25T11:19:39+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.5.0", "ext-mysqli": "*", "ext-xml": "*", "ext-pcre": "*", "ext-json": "*", "ext-ctype": "*", "ext-hash": "*"}, "platform-dev": [], "platform-overrides": {"php": "5.5"}}