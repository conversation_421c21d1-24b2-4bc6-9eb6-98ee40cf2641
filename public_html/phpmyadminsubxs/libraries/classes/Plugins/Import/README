This directory holds import plugins for phpMyAdmin. Any new plugin should
basically follow the structure presented here. The messages must use our
gettext mechanism, see https://wiki.phpmyadmin.net/pma/Gettext_for_developers.

<?php
/* vim: set expandtab sw=4 ts=4 sts=4: */
/**
 * [Name] import plugin for phpMyAdmin
 *
 * @package    PhpMyAdmin-Import
 * @subpackage [Name]
 */
namespace PhpMyAdmin\Plugins\Import;

use PhpMyAdmin\Import;
use PhpMyAdmin\Plugins\ImportPlugin;

/**
 * Handles the import for the [Name] format
 *
 * @package PhpMyAdmin-Import
 */
class Import[Name] extends ImportPlugin
{
    /**
     * optional - declare variables and descriptions
     *
     * @var type
     */
    private $_myOptionalVariable;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->setProperties();
    }

    /**
     * Sets the import plugin properties.
     * Called in the constructor.
     *
     * @return void
     */
    protected function setProperties()
    {
        $importPluginProperties = new PhpMyAdmin\Properties\Plugins\ImportPluginProperties();
        $importPluginProperties->setText('[name]');             // the name of your plug-in
        $importPluginProperties->setExtension('[ext]');         // extension this plug-in can handle
        $importPluginProperties->setOptionsText(__('Options'));

        // create the root group that will be the options field for
        // $importPluginProperties
        // this will be shown as "Format specific options"
        $importSpecificOptions = new
        PhpMyAdmin\Properties\Options\Groups\OptionsPropertyRootGroup(
            "Format Specific Options"
        );

        // general options main group
        $generalOptions = new PhpMyAdmin\Properties\Options\Groups\OptionsPropertyMainGroup(
            "general_opts"
        );

        // optional :
        // create primary items and add them to the group
        // type - one of the classes listed in libraries/properties/options/items/
        // name - form element name
        // text - description in GUI
        // size - size of text element
        // len  - maximal size of input
        // values - possible values of the item
        $leaf = new PhpMyAdmin\Properties\Options\Items\RadioPropertyItem(
            "structure_or_data"
        );
        $leaf->setValues(
            array(
                'structure' => __('structure'),
                'data' => __('data'),
                'structure_and_data' => __('structure and data')
            )
        );
        $generalOptions->addProperty($leaf);

        // add the main group to the root group
        $importSpecificOptions->addProperty($generalOptions);

        // set the options for the import plugin property item
        $importPluginProperties->setOptions($importSpecificOptions);
        $this->properties = $importPluginProperties;
    }

    /**
     * Handles the whole import logic
     *
     * @param array &$sql_data 2-element array with sql data
     *
     * @return void
     */
    public function doImport(&$sql_data = array())
    {
        // get globals (others are optional)
        global $error, $timeout_passed, $finished;

        $buffer = '';
        while (! ($finished && $i >= $len) && ! $error && ! $timeout_passed) {
            $data = Import::getNextChunk();
            if ($data === false) {
                // subtract data we didn't handle yet and stop processing
                $GLOBALS['offset'] -= strlen($buffer);
                break;
            } elseif ($data === true) {
                // Handle rest of buffer
            } else {
                // Append new data to buffer
                $buffer .= $data;
            }
            // PARSE $buffer here, post sql queries using:
            Import::runQuery($sql, $verbose_sql_with_comments, $sql_data);
        } // End of import loop
        // Commit any possible data in buffers
        Import::runQuery('', '', $sql_data);
    }


    // optional:
    /* ~~~~~~~~~~~~~~~~~~~~ Getters and Setters ~~~~~~~~~~~~~~~~~~~~ */


    /**
     * Getter description
     *
     * @return type
     */
    private function _getMyOptionalVariable()
    {
        return $this->_myOptionalVariable;
    }

    /**
     * Setter description
     *
     * @param type $my_optional_variable description
     *
     * @return void
     */
    private function _setMyOptionalVariable($my_optional_variable)
    {
        $this->_myOptionalVariable = $my_optional_variable;
    }
}
?>
