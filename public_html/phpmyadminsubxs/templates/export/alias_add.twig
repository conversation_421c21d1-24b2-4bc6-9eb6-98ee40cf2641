<table>
    <thead>
        <tr>
            <th colspan="4">{% trans 'Define new aliases' %}</th>
        </tr>
    </thead>
    <tr>
        <td>
            <label>{% trans 'Select database:' %}</label>
        </td>
        <td>
            <select id="db_alias_select"><option value=""></option></select>
        </td>
        <td>
            <input id="db_alias_name" placeholder="{% trans 'New database name' %}" disabled="1" />
        </td>
        <td>
            <button id="db_alias_button" class="ui-button ui-corner-all ui-widget" disabled="1">{% trans 'Add' %}</button>
        </td>
    </tr>
    <tr>
        <td>
            <label>{% trans 'Select table:' %}</label>
        </td>
        <td>
            <select id="table_alias_select"><option value=""></option></select>
        </td>
        <td>
            <input id="table_alias_name" placeholder="{% trans 'New table name' %}" disabled="1" />
        </td>
        <td>
            <button id="table_alias_button" class="ui-button ui-corner-all ui-widget" disabled="1">{% trans 'Add' %}</button>
        </td>
    </tr>
    <tr>
        <td>
            <label>{% trans 'Select column:' %}</label>
        </td>
        <td>
            <select id="column_alias_select"><option value=""></option></select>
        </td>
        <td>
            <input id="column_alias_name" placeholder="{% trans 'New column name' %}" disabled="1" />
        </td>
        <td>
            <button id="column_alias_button" class="ui-button ui-corner-all ui-widget" disabled="1">{% trans 'Add' %}</button>
        </td>
    </tr>
</table>
