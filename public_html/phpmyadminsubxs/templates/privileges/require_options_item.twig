<div class="item">
    {% if require_option['radio'] %}
        <input type="radio" name="ssl_type"
            id="{{ require_option['name'] }}_{{ require_option['value'] }}"
            title="{{ require_option['description'] }}"
            value="{{ require_option['value'] }}" {{ require_option['checked']|raw -}}
        />
        <label for="{{ require_option['name'] }}_{{ require_option['value'] }}">
            <code>{{ require_option['label'] }}</code>
        </label>
    {% else %}
        <label for="text_{{ require_option['name'] }}">
            <code>{{ require_option['label'] }}</code>
        </label>
        <input type="text" name="{{ require_option['name'] }}"
            id="text_{{ require_option['name'] }}" value="{{ require_option['value'] }}"
            size="80" title="{{ require_option['description'] }}"
            {%- if require_option['disabled'] %}
                disabled
            {%- endif -%}
        />
    {% endif %}
</div>
