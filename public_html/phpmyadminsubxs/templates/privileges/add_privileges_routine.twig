<input type="hidden" name="dbname" value="{{ database }}"/>

<label for="text_routinename">{% trans 'Add privileges on the following routine:' %}</label>

{%- if routines is not empty %}
    <select name="pred_routinename" class="autosubmit">
        <option value="" selected="selected">{% trans 'Use text field' %}:</option>
        {% for routine in routines %}
            <option value="{{ routine }}">{{ routine }}</option>
        {% endfor %}
    </select>
{% endif -%}

<input type="text" id="text_routinename" name="routinename" />
