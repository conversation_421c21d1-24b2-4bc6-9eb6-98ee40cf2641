<div class="item" id="div_item_{{ name }}">
    <label for="select_{{ name }}_priv">
        <code><dfn title="{{ name_for_dfn }}">{{ priv_for_header }}</dfn></code>
    </label>

    <select id="select_{{ name }}_priv" name="{{ name_for_select }}[]" multiple="multiple" size="8">
        {% for curr_col, curr_col_privs in columns %}
            <option value="{{ curr_col }}"
            {% if row[name_for_select] == 'Y' or curr_col_privs[name_for_current] %}
                selected="selected"
            {% endif %}>
                {{ curr_col }}
            </option>
        {% endfor %}
    </select>

    <em>{% trans 'Or' %}</em>
    <label for="checkbox_{{ name_for_select }}_none">
        <input type="checkbox" name="{{ name_for_select }}_none"
            id="checkbox_{{ name_for_select }}_none"
            title="{% trans %}None{% context %}None privileges{% endtrans %}" />
            {% trans %}None{% context %}None privileges{% endtrans %}
    </label>
</div>
