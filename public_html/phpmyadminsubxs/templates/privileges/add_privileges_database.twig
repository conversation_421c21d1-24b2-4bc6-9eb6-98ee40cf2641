<label for="text_dbname">{% trans 'Add privileges on the following database(s):' %}</label>

{%- if databases is not empty %}
    <select name="pred_dbname[]" multiple="multiple">
        {% for database in databases %}
            <option value="{{ Util_escapeMysqlWildcards(database) }}">
                {{ database }}
            </option>
        {% endfor %}
    </select>
{% endif -%}

<input type="text" id="text_dbname" name="dbname" />
{{ Util_showHint("Wildcards % and _ should be escaped with a \\ to use them literally."|trans) }}
