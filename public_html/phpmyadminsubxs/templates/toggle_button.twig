<div class='wrapper toggleAjax hide'>
    <div class='toggleButton'>
        <div title="{% trans 'Click to toggle' %}" class='container {{ state }}'>
            <img src="{{ pma_theme_image }}toggle-{{ text_dir }}.png" alt='' />
            <table class='nospacing nopadding'>
                <tbody>
                    <tr>
                        <td class='toggleOn'>
                            <span class='hide'>{{ link_on|raw }}</span>
                            <div>{{ toggle_on }}</div>
                        </td>
                        <td><div>&nbsp;</div></td>
                        <td class='toggleOff'>
                            <span class='hide'>{{ link_off|raw }}</span>
                            <div>{{ toggle_off }}</div>
                        </td>
                    </tr>
                </tbody>
            </table>
            <span class='hide callback'>{{ callback }}</span>
            <span class='hide text_direction'>{{ text_dir }}</span>
        </div>
    </div>
</div>
