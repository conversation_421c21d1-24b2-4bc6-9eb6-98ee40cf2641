<select id="field_{{ column_number }}_{{ ci - ci_offset }}"
    size="1"
    name="field_mimetype[{{ column_number }}]">
    <option value="">&nbsp;</option>
    {% if available_mime['mimetype'] is defined
        and available_mime['mimetype'] is iterable %}
        {% for mimetype in available_mime['mimetype'] %}
            {% set checked = column_meta['Field'] is defined
                and mime_map[column_meta['Field']]['mimetype'] is defined
                and mime_map[column_meta['Field']]['mimetype'] == mimetype|replace({'/': '_'})
                ? ' selected' %}
            <option value="{{ mimetype|replace({'/': '_'}) }}"{{ checked }}>
                {{ mimetype|lower }}
            </option>
        {% endfor %}
    {% endif %}
</select>
