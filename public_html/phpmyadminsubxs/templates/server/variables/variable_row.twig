<tr class="var-row {{ row_class }}" data-filter-row="{{ name | upper }}">
    <td class="var-action">
    {% if editable %}
        <a href="#" data-variable="{{ name }}" class="editLink">{{ Util_getIcon('b_edit', 'Edit'|trans) }}</a>
    {% else %}
        <span title="{% trans 'This is a read-only variable and can not be edited' %}" class="read_only_var">
            {{ Util_getIcon('bd_edit', 'Edit'|trans) }}
        </span>
    {% endif %}
    </td>
    <td class="var-name">
    {% if doc_link != null %}
        <span title="{{ name|replace({'_': ' '}) }}">
            {{ Util_showMySQLDocu(doc_link[1], false, doc_link[2] ~ '_' ~ doc_link[0], true) }}
            {{ name|e|replace({'_': '&nbsp;'})|raw }}
            </a>
        </span>
    {% else %}
        {{ name|replace({'_': ' '}) }}
    {% endif %}
    </td>
    <td class="var-value value{{ is_superuser ? ' editable' }}">
    {% if is_html_formatted == false %}
        {{ value|e|replace({',': ',&#8203;'})|raw }}
    {% else %}
        {{ value|raw }}
    {% endif %}
    </td>
</tr>
