{# Displays the sub-page heading #}
{% include 'server/sub_page_header.twig' with {
    'type': dbstats ? 'database_statistics' : 'databases'
} only %}

{# Displays For Create database #}
{% if show_create_db %}
    {% include 'server/databases/create.twig' with {
        'is_create_db_priv': is_create_db_priv,
        'dbstats': dbstats,
        'db_to_create': db_to_create,
        'server_collation': server_collation,
        'dbi': dbi,
        'disable_is': disable_is
    } only %}
{% endif %}

{% include 'filter.twig' with {'filter_value': ''} only %}

{# Displays the page #}
{% if databases is not null %}
    {{ databases|raw }}
{% else %}
    <p>{% trans 'No databases' %}</p>
{% endif %}
