<tr>
    <td class="vmiddle">
        <strong>{{ myfield }}</strong>
        <input type="hidden" name="fields_name[{{ myfield_md5 }}]"
               value="{{ myfield }}"/>
    </td>

    <td>
        {% include 'table/relation/relational_dropdown.twig' with {
            'name': 'destination_db[' ~ myfield_md5 ~ ']',
            'title': 'Database'|trans,
            'values': databases,
            'foreign': foreign_db
        } only %}

        {% include 'table/relation/relational_dropdown.twig' with {
            'name': 'destination_table[' ~ myfield_md5 ~ ']',
            'title': 'Table'|trans,
            'values': tables,
            'foreign': foreign_table
        } only %}

        {% include 'table/relation/relational_dropdown.twig' with {
            'name': 'destination_column[' ~ myfield_md5 ~ ']',
            'title': 'Column'|trans,
            'values': columns,
            'foreign': foreign_column
        } only %}
    </td>
</tr>
