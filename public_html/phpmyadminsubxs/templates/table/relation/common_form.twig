<form method="post" action="tbl_relation.php">
    {{ Url_getHiddenInputs(db, table) }}
    {# InnoDB #}
    {% if Util_isForeignKeySupported(tbl_storage_engine) %}
        <fieldset>
            <legend>{% trans 'Foreign key constraints' %}</legend>
            <div class="responsivetable jsresponsive">
            <table id="foreign_keys" class="relationalTable">
                <thead><tr>
                    <th>{% trans 'Actions' %}</th>
                    <th>{% trans 'Constraint properties' %}</th>
                    {% if tbl_storage_engine|upper == 'INNODB' %}
                        <th>
                            {% trans 'Column' %}
                            {{ Util_showHint('Creating a foreign key over a non-indexed column would automatically create an index on it. Alternatively, you can define an index below, before creating the foreign key.'|trans) }}
                        </th>
                    {% else %}
                        <th>
                            {% trans 'Column' %}
                            {{ Util_showHint('Only columns with index will be displayed. You can define an index below.'|trans) }}
                        </th>
                    {% endif %}
                    <th colspan="3">
                        {% trans 'Foreign key constraint' %}
                        ({{ tbl_storage_engine }})
                    </th>
                </tr>
                <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th>{% trans 'Database' %}</th>
                    <th>{% trans 'Table' %}</th>
                    <th>{% trans 'Column' %}</th>
                </tr></thead>
                {% set i = 0 %}
                {% if existrel_foreign is not empty %}
                    {% for key, one_key in existrel_foreign %}
                        {# Foreign database dropdown #}
                        {% set foreign_db = one_key['ref_db_name'] is defined
                            and one_key['ref_db_name'] is not null
                            ? one_key['ref_db_name'] : db %}
                        {% set foreign_table = false %}
                        {% if foreign_db %}
                            {% set foreign_table = one_key['ref_table_name'] is defined
                                and one_key['ref_table_name'] is not null
                                ? one_key['ref_table_name'] : false %}
                        {% endif %}
                        {% set unique_columns = [] %}
                        {% if foreign_db and foreign_table %}
                            {% set table_obj = Table_get(foreign_table, foreign_db) %}
                            {% set unique_columns = table_obj.getUniqueColumns(false, false) %}
                        {% endif %}
                        {% include 'table/relation/foreign_key_row.twig' with {
                            'i': i,
                            'one_key': one_key,
                            'column_array': column_array,
                            'options_array': options_array,
                            'tbl_storage_engine': tbl_storage_engine,
                            'db': db,
                            'table': table,
                            'url_params': url_params,
                            'databases': databases,
                            'foreign_db': foreign_db,
                            'foreign_table': foreign_table,
                            'unique_columns': unique_columns
                        } only %}
                        {% set i = i + 1 %}
                    {% endfor %}
                {% endif %}
                {% include 'table/relation/foreign_key_row.twig' with {
                    'i': i,
                    'one_key': [],
                    'column_array': column_array,
                    'options_array': options_array,
                    'tbl_storage_engine': tbl_storage_engine,
                    'db': db,
                    'table': table,
                    'url_params': url_params,
                    'databases': databases,
                    'foreign_db': foreign_db,
                    'foreign_table': foreign_table,
                    'unique_columns': unique_columns
                } only %}
                {% set i = i + 1 %}
                <tr>
                    <th colspan="6">
                        <a class="formelement clearfloat add_foreign_key" href="">
                            {% trans '+ Add constraint' %}
                    </td>
                </tr>
            </table>
            </div>
        </fieldset>
    {% endif %}

    {% if cfg_relation['relwork'] %}
        {% if Util_isForeignKeySupported(tbl_storage_engine) %}
            {{ Util_getDivForSliderEffect('ir_div', 'Internal relationships'|trans) }}
        {% endif %}

        <fieldset>
            <legend>
                {% trans 'Internal relationships' %}
                {{ Util_showDocu('config', 'cfg_Servers_relation') }}
            </legend>
            <table id="internal_relations" class="relationalTable">
                <tr>
                    <th>{% trans 'Column' %}</th>
                    <th>{% trans 'Internal relation' %}
                        {% if Util_isForeignKeySupported(tbl_storage_engine) %}
                            {{ Util_showHint('An internal relation is not necessary when a corresponding FOREIGN KEY relation exists.'|trans) }}
                        {% endif %}
                    </th>
                    {% set saved_row_cnt = save_row|length - 1 %}
                    {% for i in 0..saved_row_cnt %}
                        {% set myfield = save_row[i]['Field'] %}
                        {# Use an md5 as array index to avoid having special characters
                           in the name attribute (see bug #1746964 ) #}
                        {% set myfield_md5 = md5(myfield) %}

                        {% set foreign_table = false %}
                        {% set foreign_column = false %}

                        {# Database dropdown #}
                        {% if existrel[myfield] is defined %}
                            {% set foreign_db = existrel[myfield]['foreign_db'] %}
                        {% else %}
                            {% set foreign_db = db %}
                        {% endif %}

                        {# Table dropdown #}
                        {% set tables = [] %}
                        {% if foreign_db %}
                            {% if existrel[myfield] is defined %}
                                {% set foreign_table = existrel[myfield]['foreign_table'] %}
                            {% endif %}
                            {% set tables = dbi.getTables(foreign_db) %}
                        {% endif %}

                        {# Column dropdown #}
                        {% set unique_columns = [] %}
                        {% if foreign_db and foreign_table %}
                            {% if existrel[myfield] is defined %}
                                {% set foreign_column = existrel[myfield]['foreign_field'] %}
                            {% endif %}
                            {% set table_obj = Table_get(foreign_table, foreign_db) %}
                            {% set unique_columns = table_obj.getUniqueColumns(false, false) %}
                        {% endif %}

                        {% include 'table/relation/internal_relational_row.twig' with {
                            'myfield': myfield,
                            'myfield_md5': myfield_md5,
                            'databases': databases,
                            'tables': tables,
                            'columns': unique_columns,
                            'foreign_db': foreign_db,
                            'foreign_table': foreign_table,
                            'foreign_column': foreign_column
                        } only %}
                    {% endfor %}
            </table>
        </fieldset>
        {% if Util_isForeignKeySupported(tbl_storage_engine) %}
            </div>
        {% endif %}
    {% endif %}

    {% if cfg_relation['displaywork'] %}
        {% set disp = Relation_getDisplayField(db, table) %}
        <fieldset>
            <label>{% trans 'Choose column to display:' %}</label>
            <select name="display_field">
                <option value="">---</option>
                {% for row in save_row %}
                    <option value="{{ row['Field'] }}"
                        {%- if disp is defined and row['Field'] == disp %}
                            selected="selected"
                        {%- endif %}>
                        {{ row['Field'] }}
                    </option>
                {% endfor %}
            </select>
        </fieldset>
    {% endif %}

    <fieldset class="tblFooters">
        <input type="button" class="preview_sql" value="{% trans 'Preview SQL' %}" />
        <input type="submit" value="{% trans 'Save' %}" />
    </fieldset>
</form>
