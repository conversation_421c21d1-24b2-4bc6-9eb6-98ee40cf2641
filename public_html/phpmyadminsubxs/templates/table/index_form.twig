<form action="tbl_indexes.php"
    method="post"
    name="index_frm"
    id="index_frm"
    class="ajax">

    {{ Url_getHiddenInputs(form_params) }}

    <fieldset id="index_edit_fields">
        <div class="index_info">
            <div>
                <div class="label">
                    <strong>
                        <label for="input_index_name">
                            {% trans 'Index name:' %}
                            {{ Util_showHint('"PRIMARY" <b>must</b> be the name of and <b>only of</b> a primary key!'|trans) }}
                        </label>
                    </strong>
                </div>

                <input type="text"
                    name="index[Key_name]"
                    id="input_index_name"
                    size="25"
                    maxlength="64"
                    value="{{ index.getName() }}"
                    onfocus="this.select()" />
            </div>

            <div>
                <div class="label">
                    <strong>
                        <label for="select_index_choice">
                            {% trans 'Index choice:' %}
                            {{ Util_showMySQLDocu('ALTER_TABLE') }}
                        </label>
                    </strong>
                </div>
                {{ index.generateIndexChoiceSelector(create_edit_table)|raw }}
            </div>

            {{ Util_getDivForSliderEffect('indexoptions', 'Advanced Options'|trans) }}

            <div>
                <div class="label">
                    <strong>
                        <label for="input_key_block_size">
                            {% trans 'Key block size:' %}
                        </label>
                    </strong>
                </div>

                <input type="text"
                    name="index[Key_block_size]"
                    id="input_key_block_size"
                    size="30"
                    value="{{ index.getKeyBlockSize() }}" />
            </div>

            <div>

                <div class="label">
                    <strong>
                        <label for="select_index_type">
                            {% trans 'Index type:' %}
                            {{ Util_showMySQLDocu('ALTER_TABLE') }}
                        </label>
                    </strong>
                </div>
                {{ index.generateIndexTypeSelector()|raw }}
            </div>

            <div>
                <div class="label">
                    <strong>
                        <label for="input_parser">
                            {% trans 'Parser:' %}
                        </label>
                    </strong>
                </div>

                <input type="text"
                    name="index[Parser]"
                    id="input_parse"
                    size="30"
                    value="{{ index.getParser() }}" />
            </div>

            <div>
                <div class="label">
                    <strong>
                        <label for="input_index_comment">
                            {% trans 'Comment:' %}
                        </label>
                    </strong>
                </div>

                <input type="text"
                    name="index[Index_comment]"
                    id="input_index_comment"
                    size="30"
                    maxlength="1024"
                    value="{{ index.getComment() }}" />
            </div>
        </div>
        <!-- end of indexoptions div -->

        <div class="clearfloat"></div>

        <table id="index_columns">
            <thead>
                <tr>
                    <th></th>
                    <th>
                        {% trans 'Column' %}
                    </th>
                    <th>
                        {% trans 'Size' %}
                    </th>
                </tr>
            </thead>
            {% set spatial_types = [
                'geometry',
                'point',
                'linestring',
                'polygon',
                'multipoint',
                'multilinestring',
                'multipolygon',
                'geomtrycollection'
            ] %}
            <tbody>
                {% for column in index.getColumns() %}
                    <tr class="noclick">
                        <td>
                            <span class="drag_icon" title="{% trans 'Drag to reorder' %}"></span>
                        </td>
                        <td>
                            <select name="index[columns][names][]">
                                <option value="">
                                    -- {% trans 'Ignore' %} --
                                </option>
                                {% for field_name, field_type in fields %}
                                    {% if (index.getChoice() != 'FULLTEXT'
                                            or field_type matches '/(char|text)/i')
                                        and (index.getChoice() != 'SPATIAL'
                                            or field_type in spatial_types) %}

                                        <option value="{{ field_name }}"
                                            {%- if field_name == column.getName() %}
                                                selected="selected"
                                            {%- endif %}>
                                            {{ field_name }} [{{ field_type }}]
                                        </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </td>
                        <td>
                            <input type="text"
                                size="5"
                                onfocus="this.select()"
                                name="index[columns][sub_parts][]"
                                value="{{ index.getChoice() != 'SPATIAL' ?
                                    column.getSubPart() }}" />
                        </td>
                    </tr>
                {% endfor %}
                {% if add_fields > 0 %}
                    {% for i in range(1, add_fields) %}
                        <tr class="noclick">
                            <td>
                                <span class="drag_icon" title="{% trans 'Drag to reorder' %}"></span>
                            </td>
                            <td>
                                <select name="index[columns][names][]">
                                    <option value="">-- {% trans 'Ignore' %} --</option>
                                    {% set j = 0 %}
                                    {% for field_name, field_type in fields %}
                                        {% if create_edit_table %}
                                            {% set col_index = field_type[1] %}
                                            {% set field_type = field_type[0] %}
                                        {% endif %}
                                        {% set j = j + 1 %}
                                        <option value="{{ col_index is defined ?
                                            col_index : field_name }}"
                                            {{- j == i ? ' selected="selected"' }}>
                                            {{ field_name }} [{{ field_type }}]
                                        </option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <input type="text"
                                    size="5"
                                    onfocus="this.select()"
                                    name="index[columns][sub_parts][]"
                                    value="" />
                            </td>
                        </tr>
                    {% endfor %}
                {% endif %}
            </tbody>
        </table>
        <div class="add_more">

            <div class="slider"></div>
            <div class="add_fields hide">
                <input type="submit"
                    id="add_fields"
                    value="{{ 'Add %s column(s) to index'|trans|format(1) }}" />
            </div>
        </div>
    </fieldset>
    <fieldset class="tblFooters">
        <button type="submit" id="preview_index_frm">{% trans 'Preview SQL' %}</button>
        <input type="submit" id="save_index_frm" value="{% trans 'Go' %}" />
    </fieldset>
</form>
