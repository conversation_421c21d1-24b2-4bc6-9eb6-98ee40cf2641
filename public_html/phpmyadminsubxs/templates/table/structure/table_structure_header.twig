<thead>
    <tr>
        <th class="print_ignore"></th>
        <th>#</th>
        <th>{% trans 'Name' %}</th>
        <th>{% trans 'Type' %}</th>
        <th>{% trans 'Collation' %}</th>
        <th>{% trans 'Attributes' %}</th>
        <th>{% trans 'Null' %}</th>
        <th>{% trans 'Default' %}</th>
        {% if show_column_comments -%}
            <th>{% trans 'Comments' %}</th>
        {%- endif %}
        <th>{% trans 'Extra' %}</th>
        {# @see tbl_structure.js, function moreOptsMenuResize() #}
        {% if not db_is_system_schema and not tbl_is_view %}
            <th colspan="{{ Util_showIcons('ActionLinksMode') ? '8' : '9' -}}
                " class="action print_ignore">{% trans 'Action' %}</th>
        {% endif %}
    </tr>
</thead>
