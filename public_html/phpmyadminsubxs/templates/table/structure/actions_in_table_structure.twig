<td class="print_ignore"><ul class="table-structure-actions resizable-menu">
    {% if hide_structure_actions %}
        <li class="submenu shown">
            <a href="#" class="tab nowrap">{{ Util_getIcon('b_more', 'More'|trans) }}</a>
            <ul>
    {% endif %}
    {# Add primary #}
    {% include 'table/structure/action_row_in_structure_table.twig' with {
        'type': type,
        'tbl_storage_engine': tbl_storage_engine,
        'class': 'primary nowrap',
        'has_field': primary and primary.hasColumn(field_name),
        'has_link_class': true,
        'url_query': url_query,
        'primary': primary,
        'syntax': 'ADD PRIMARY KEY',
        'message': 'A primary key has been added on %s.'|trans,
        'action': 'Primary',
        'titles': titles,
        'row': row,
        'is_primary': true,
        'table': table
    } only %}

    {# Add unique #}
    {% include 'table/structure/action_row_in_structure_table.twig' with {
        'type': type,
        'tbl_storage_engine': tbl_storage_engine,
        'class': 'add_unique unique nowrap',
        'has_field': field_name in columns_with_unique_index,
        'has_link_class': false,
        'url_query': url_query,
        'primary': primary,
        'syntax': 'ADD UNIQUE',
        'message': 'An index has been added on %s.'|trans,
        'action': 'Unique',
        'titles': titles,
        'row': row,
        'is_primary': false,
        'table': table
    } only %}

    {# Add index #}
    {% include 'table/structure/action_row_in_structure_table.twig' with {
        'type': type,
        'tbl_storage_engine': tbl_storage_engine,
        'class': 'add_index nowrap',
        'has_field': false,
        'has_link_class': false,
        'url_query': url_query,
        'primary': primary,
        'syntax': 'ADD INDEX',
        'message': 'An index has been added on %s.'|trans,
        'action': 'Index',
        'titles': titles,
        'row': row,
        'is_primary': false,
        'table': table
    } only %}

    {# Add spatial #}
    {% set spatial_types = [
        'geometry',
        'point',
        'linestring',
        'polygon',
        'multipoint',
        'multilinestring',
        'multipolygon',
        'geomtrycollection'
    ] %}
    {% include 'table/structure/action_row_in_structure_table.twig' with {
        'type': type,
        'tbl_storage_engine': tbl_storage_engine,
        'class': 'spatial nowrap',
        'has_field': type not in spatial_types and
            (tbl_storage_engine == 'MYISAM' or mysql_int_version >= 50705),
        'has_link_class': false,
        'url_query': url_query,
        'primary': primary,
        'syntax': 'ADD SPATIAL',
        'message': 'An index has been added on %s.'|trans,
        'action': 'Spatial',
        'titles': titles,
        'row': row,
        'is_primary': false,
        'table': table
    } only %}

    {# FULLTEXT is possible on TEXT, CHAR and VARCHAR #}
    <li class="fulltext nowrap">
    {% if tbl_storage_engine is not empty and (
            tbl_storage_engine == 'MYISAM'
            or tbl_storage_engine == 'ARIA'
            or tbl_storage_engine == 'MARIA'
            or (tbl_storage_engine == 'INNODB' and mysql_int_version >= 50604)
        ) and ('text' in type or 'char' in type) %}
        <a rel="samepage" class="ajax add_key add_fulltext_anchor" href="tbl_structure.php"
            data-post="{{- url_query|raw }}&amp;add_key=1&amp;sql_query=
            {{- ('ALTER TABLE ' ~ Util_backquote(table)
                ~ ' ADD FULLTEXT(' ~ Util_backquote(row['Field'])
                ~ ');')|url_encode }}&amp;message_to_show=
            {{- ('An index has been added on %s.'|trans|format(row['Field']|e))|url_encode }}">
            {{ titles['IdxFulltext']|raw }}
        </a>
    {% else %}
        {{ titles['NoIdxFulltext']|raw }}
    {% endif %}
    </li>

    {# Distinct value action #}
    <li class="browse nowrap">
        <a href="sql.php" data-post="{{ url_query|raw }}&amp;sql_query=
            {{- ('SELECT COUNT(*) AS ' ~ Util_backquote('Rows'|trans)
                ~ ', ' ~ Util_backquote(row['Field'])
                ~ ' FROM ' ~ Util_backquote(table)
                ~ ' GROUP BY ' ~ Util_backquote(row['Field'])
                ~ ' ORDER BY ' ~ Util_backquote(row['Field']))|url_encode -}}
            &amp;is_browse_distinct=1">
            {{ titles['DistinctValues']|raw }}
        </a>
    </li>
    {% if central_columns_work %}
        <li class="browse nowrap">
        {% if is_in_central_columns %}
            <a href="#" class="central_columns remove_button">
                {{ Util_getIcon('centralColumns_delete', 'Remove from central columns'|trans) }}
            </a>
        {% else %}
            <a href="#" class="central_columns add_button">
                {{ Util_getIcon('centralColumns_add', 'Add to central columns'|trans) }}
            </a>
        {% endif %}
        </li>
    {% endif %}
    {% if hide_structure_actions %}
            </ul>
        </li>
    {% endif %}
</ul></td>
