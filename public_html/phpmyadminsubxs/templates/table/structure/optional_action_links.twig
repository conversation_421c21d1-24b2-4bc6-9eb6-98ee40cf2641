<a href="#" id="printView">{{ Util_getIcon('b_print', 'Print'|trans, true) }}</a>
{% if not tbl_is_view and not db_is_system_schema %}
    <a href="sql.php" data-post="{{ url_query|raw }}&amp;session_max_rows=all&amp;sql_query=
        {{- ('SELECT * FROM ' ~ Util_backquote(table) ~ ' PROCEDURE ANALYSE()')|url_encode -}}
        " style="margin-right: 0;">
        {{ Util_getIcon(
            'b_tblanalyse',
            'Propose table structure'|trans,
            true
        ) }}
    </a>
    {{ Util_showMySQLDocu('procedure_analyse') }}
    {% if is_active %}
        <a href="tbl_tracking.php{{ url_query|raw }}">
            {{ Util_getIcon('eye', 'Track table'|trans, true) }}
        </a>
    {% endif %}
    <a href="#" id="move_columns_anchor">
        {{ Util_getIcon('b_move', 'Move columns'|trans, true) }}
    </a>
    <a href="normalization.php{{ url_query|raw }}">
        {{ Util_getIcon('normalize', 'Normalize'|trans, true) }}
    </a>
{% endif %}
{% if tbl_is_view and not db_is_system_schema %}
    {% if is_active %}
        <a href="tbl_tracking.php{{ url_query|raw }}">
            {{ Util_getIcon('eye', 'Track view'|trans, true) }}
        </a>
    {% endif %}
{% endif %}
