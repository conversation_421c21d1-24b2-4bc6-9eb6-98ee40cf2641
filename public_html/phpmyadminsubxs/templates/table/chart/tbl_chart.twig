<script type="text/javascript">
    url_query = '{{ url_query }}';
</script>
{# Display Chart options #}
<div id="div_view_options">
    <form method="post" id="tblchartform" action="tbl_chart.php" class="ajax">
        {{ Url_getHiddenInputs(url_params) }}
        <fieldset>
            <legend>
                {% trans 'Display chart' %}
            </legend>
            <div class="chartOption">
                <div class="formelement">
                    <input type="radio" name="chartType" value="bar" id="radio_bar" />
                    <label for ="radio_bar">{% trans %}Bar{% context %}Chart type{% endtrans %}</label>
                </div>
                <div class="formelement">
                    <input type="radio" name="chartType" value="column" id="radio_column" />
                    <label for ="radio_column">{% trans %}Column{% context %}Chart type{% endtrans %}</label>
                </div>
                <div class="formelement">
                    <input type="radio" name="chartType" value="line" id="radio_line" checked="checked" />
                    <label for ="radio_line">{% trans %}Line{% context %}Chart type{% endtrans %}</label>
                </div>
                <div class="formelement">
                    <input type="radio" name="chartType" value="spline" id="radio_spline" />
                    <label for ="radio_spline">{% trans %}Spline{% context %}Chart type{% endtrans %}</label>
                </div>
                <div class="formelement">
                    <input type="radio" name="chartType" value="area" id="radio_area" />
                    <label for ="radio_area">{% trans %}Area{% context %}Chart type{% endtrans %}</label>
                </div>
                <span class="span_pie hide">
                    <input type="radio" name="chartType" value="pie" id="radio_pie" />
                    <label for ="radio_pie">{% trans %}Pie{% context %}Chart type{% endtrans %}</label>
                </span>
                <span class="span_timeline hide">
                    <input type="radio" name="chartType" value="timeline" id="radio_timeline" />
                    <label for ="radio_timeline">{% trans %}Timeline{% context %}Chart type{% endtrans %}</label>
                </span>
                <span class="span_scatter hide">
                <input type="radio" name="chartType" value="scatter" id="radio_scatter" />
                <label for ="radio_scatter">{% trans %}Scatter{% context %}Chart type{% endtrans %}</label>
                </span>
                <br /><br />
                <span class="barStacked hide">
                    <input type="checkbox" name="barStacked" value="1" id="checkbox_barStacked" />
                    <label for ="checkbox_barStacked">{% trans 'Stacked' %}</label>
                </span>
                <br /><br />
                <label for ="chartTitle">{% trans 'Chart title:' %}</label>
                <input type="text" name="chartTitle" id="chartTitle" />
            </div>
            {% set xaxis = null %}
            <div class="chartOption">
                <label for="select_chartXAxis">{% trans 'X-Axis:' %}</label>
                <select name="chartXAxis" id="select_chartXAxis">
                    {% for idx, key in keys %}
                        {% if xaxis is same as(null) %}
                            {% set xaxis = idx %}
                        {% endif %}
                        {% if xaxis is same as(idx) %}
                            <option value="{{ idx }}" selected="selected">{{ key }}</option>
                        {% else %}
                            <option value="{{ idx }}">{{ key }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
                <br />
                <label for="select_chartSeries">
                    {% trans 'Series:' %}
                </label>
                <select name="chartSeries" id="select_chartSeries" multiple="multiple">
                    {% for idx, key in keys %}
                        {% if fields_meta[idx].type in numeric_types %}
                            {% if idx == xaxis and numeric_column_count > 1 %}
                                <option value="{{ idx }}">{{ key }}</option>
                            {% else %}
                                <option value="{{ idx }}" selected="selected">{{ key }}</option>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                </select>
                <input type="hidden" name="dateTimeCols" value="
                    {% set date_time_types = ['date', 'datetime', 'timestamp'] %}
                    {% for idx, key in keys %}
                        {% if fields_meta[idx].type in date_time_types %}
                            {{ idx ~ ' ' }}
                        {% endif %}
                    {% endfor %}"
                />
                <input type="hidden" name="numericCols" value="
                    {% for idx, key in keys %}
                        {% if fields_meta[idx].type in numeric_types %}
                            {{ idx ~ ' ' }}
                        {% endif %}
                    {% endfor %}"
                />
            </div>
            <div class="chartOption">
                <label for="xaxis_panel">
                    {% trans 'X-Axis label:' %}
                </label>
                <input style="margin-top:0;" type="text" name="xaxis_label" id="xaxis_label" value="{{ xaxis == -1 ? 'X Values'|trans : keys[xaxis] }}" />
                <br />
                <label for="yaxis_label">
                    {% trans 'Y-Axis label:' %}
                </label>
                <input type="text" name="yaxis_label" id="yaxis_label" value="{% trans 'Y Values' %}" />
                <br />
            </div>
            <div class="clearfloat"></div>
            <div>
                <input type="checkbox" id="chkAlternative" name="chkAlternative" value="alternativeFormat" />
                <label for="chkAlternative">{% trans 'Series names are in a column' %}</label>
                <br />
                <label for="select_seriesColumn">
                    {% trans 'Series column:' %}
                </label>
                <select name="chartSeriesColumn" id="select_seriesColumn" disabled>
                    {% for idx, key in keys %}
                        <option value="{{ idx }}"
                            {% if idx == 1 %}
                                selected="selected"
                            {% endif %}>
                                {{ key }}
                        </option>
                        {% set series_column = idx %}
                    {% endfor %}
                </select>
                <label for="select_valueColumn">
                    {% trans 'Value Column:' %}
                </label>
                <select name="chartValueColumn" id="select_valueColumn" disabled>
                    {% set selected = false %}
                    {% for idx, key in keys %}
                        {% if fields_meta[idx].type in numeric_types %}
                            {% if not selected and idx != xaxis and idx != series_column %}
                                <option value="{{ idx }}" selected="selected">{{ key }}</option>
                                {% set selected = true %}
                            {% else %}
                                <option value="{{ idx }}">{{ key }}</option>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
            {{ Util_getStartAndNumberOfRowsPanel(sql_query) }}
            <div class="clearfloat"></div>
            <div id="resizer" style="width:600px; height:400px;">
                <div style="position: absolute; right: 10px; top: 10px; cursor: pointer; z-index: 1000;">
                    <a class="disableAjax" id="saveChart" href="#" download="chart.png">
                        {{ Util_getImage('b_saveimage', 'Save chart as image'|trans) }}
                    </a>
                </div>
                <div id="querychart" dir="ltr">
                </div>
            </div>
        </fieldset>
    </form>
</div>
