<form method="post" action="tbl_tracking.php{{ url_query|raw }}">
    {{ Url_getHiddenInputs(db, table) }}
    <select name="table" class="autosubmit">
        {% for entry in entries %}
            <option value="{{ entry.table_name }}"
                {{- entry.table_name == selected_table ? ' selected' }}>
                {{ entry.db_name }} . {{ entry.table_name }}
                {% if entry.is_tracked %}
                    ({% trans 'active' %})
                {% else %}
                    ({% trans 'not active' %})
                {% endif %}
            </option>
        {% endfor %}
    </select>
    <input type="hidden" name="show_versions_submit" value="1">
</form>
