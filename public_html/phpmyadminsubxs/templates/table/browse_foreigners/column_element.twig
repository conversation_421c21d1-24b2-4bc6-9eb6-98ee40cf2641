<td{{ nowrap ? ' class="nowrap"' }}>
    {{ is_selected ? '<strong>' }}
        <a class="foreign_value" data-key="{{ keyname }}" href="#" title="
            {%- trans 'Use this value' %}{{ title is not empty ? ': ' ~ title }}">
            {% if nowrap %}
                {{ keyname }}
            {% else %}
                {{ description }}
            {% endif %}
        </a>
    {{ is_selected ? '</strong>' }}
</td>
