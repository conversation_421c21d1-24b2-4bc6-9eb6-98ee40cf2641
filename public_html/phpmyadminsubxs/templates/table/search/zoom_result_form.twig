<form method="post" action="tbl_zoom_select.php" name="displayResultForm" id="zoom_display_form" class="ajax">
    {{ Url_getHiddenInputs(db, table) }}
    <input type="hidden" name="goto" value="{{ goto }}" />
    <input type="hidden" name="back" value="tbl_zoom_select.php" />

    <fieldset id="displaySection">
        <legend>{% trans 'Browse/Edit the points' %}</legend>

        {# JSON encode the data(query result) #}
        <center>
            {% if zoom_submit and data is not empty %}
                <div id="resizer">
                    <center>
                        <a id="help_dialog" href="#">
                            {% trans 'How to use' %}
                        </a>
                    </center>
                    <div id="querydata" class="hide">
                        {{ data_json }}
                    </div>
                    <div id="querychart"></div>
                    <button class="button-reset">
                        {% trans 'Reset zoom' %}
                    </button>
                </div>
            {% endif %}
        </center>

        {# Displays rows in point edit form #}
        <div id="dataDisplay" class="hide">
            <table>
                <thead>
                <tr>
                    <th>{% trans 'Column' %}</th>
                    <th>{% trans 'Null' %}</th>
                    <th>{% trans 'Value' %}</th>
                </tr>
                </thead>
                <tbody>
                {% for column_index in 0..column_names|length - 1 %}
                    {% set field_popup = column_names[column_index] %}
                    {% set foreign_data = Relation_getForeignData(
                        foreigners,
                        field_popup,
                        false,
                        '',
                        ''
                    ) %}
                    <tr class="noclick">
                        <th>{{ column_names[column_index] }}</th>
                        {# Null checkbox if column can be null #}
                        <th>
                            {% if column_null_flags[column_index] == 'YES' %}
                                <input type="checkbox" class="checkbox_null"
                                    name="criteriaColumnNullFlags[{{ column_index }}]"
                                    id="edit_fields_null_id_{{ column_index }}" />
                            {% endif %}
                        </th>
                        {# Column's Input box #}
                        <th>
                            {% include 'table/search/input_box.twig' with {
                                'str': '',
                                'column_type': column_types[column_index],
                                'column_id': column_types[column_index] ? 'edit_fieldID_' : 'fieldID_',
                                'in_zoom_search_edit': true,
                                'foreigners': foreigners,
                                'column_name': field_popup,
                                'column_name_hash': md5(field_popup),
                                'foreign_data': foreign_data,
                                'table': table,
                                'column_index': column_index,
                                'foreign_max_limit': foreign_max_limit,
                                'criteria_values': '',
                                'db': db,
                                'titles': titles,
                                'in_fbs': false
                            } only %}
                        </th>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    <input type="hidden" id="queryID" name="sql_query" />
</form>
