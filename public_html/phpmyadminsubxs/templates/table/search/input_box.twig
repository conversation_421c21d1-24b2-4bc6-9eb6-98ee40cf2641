{# Get inputbox based on different column types (Foreign key, geometrical, enum) #}
{% if foreigners and Relation_searchColumnInForeigners(foreigners, column_name) %}
    {% if foreign_data['disp_row'] is iterable %}
        <select name="criteriaValues[{{ column_index }}]"
            id="{{ column_id }}{{ column_index }}">
            {{ Relation_foreignDropdown(
                foreign_data['disp_row'],
                foreign_data['foreign_field'],
                foreign_data['foreign_display'],
                '',
                foreign_max_limit
            ) }}
        </select>
    {% elseif foreign_data['foreign_link'] == true %}
        <input type="text"
            id="{{ column_id }}{{ column_index }}"
            name="criteriaValues[{{ column_index }}]"
            id="field_{{ column_name_hash }}[{{ column_index }}]"
            class="textfield"
            {% if criteria_values[column_index] is defined %}
                value="{{ criteria_values[column_index] }}"
            {% endif %} />
        <a class="ajax browse_foreign" href="browse_foreigners.php" data-post="
            {{- Url_getCommon({'db': db, 'table': table}, '') -}}
            &amp;field={{ column_name|url_encode }}&amp;fieldkey=
            {{- column_index }}&amp;fromsearch=1">
            {{ titles['Browse']|replace({"'": "\\'"})|raw }}
        </a>
    {% endif %}
{% elseif column_type in Util_getGISDatatypes() %}
    <input type="text"
        name="criteriaValues[{{ column_index }}]"
        size="40"
        class="textfield"
        id="field_{{ column_index }}" />
    {% if in_fbs %}
        {% set edit_url = 'gis_data_editor.php' ~ Url_getCommon() %}
        {% set edit_str = Util_getIcon('b_edit', 'Edit/Insert'|trans) %}
        <span class="open_search_gis_editor">
            {{ Util_linkOrButton(edit_url, edit_str, [], '_blank') }}
        </span>
    {% endif %}
{% elseif column_type starts with 'enum'
    or (column_type starts with 'set' and in_zoom_search_edit) %}
    {% set in_zoom_search_edit = false %}
    {% set value = column_type|e|slice(5, -1)|replace({'&#039;': ''})|split(', ') %}
    {% set cnt_value = value|length %}
    {#
    Enum in edit mode   --> dropdown
    Enum in search mode --> multiselect
    Set in edit mode    --> multiselect
    Set in search mode  --> input (skipped here, so the 'else' section would handle it)
    #}
    {% if (column_type starts with 'enum' and not in_zoom_search_edit)
        or (column_type starts with 'set' and in_zoom_search_edit) %}
        <select name="criteriaValues[{{ column_index }}]"
            id="{{ column_id }}{{ column_index }}">
    {% else %}
        <select name="criteriaValues[{{ column_index }}]"
            id="{{ column_id }}{{ column_index }}"
            multiple="multiple"
            size="{{ min(3, cnt_value) }}">
    {% endif %}
    {# Add select options #}
    <option value=""></option>
    {% for i in 0..cnt_value - 1 %}
        {% if criteria_values[column_index] is defined
            and criteria_values[column_index] is iterable
            and value[i] in criteria_values[column_index] %}
            <option value="{{ value[i]|raw }}" selected>
                {{ value[i]|raw }}
            </option>
        {% else %}
            <option value="{{ value[i]|raw }}">
                {{ value[i]|raw }}
            </option>
        {% endif %}
    {% endfor %}
    </select>
{% else %}
    {% set the_class = 'textfield' %}
    {% if column_type == 'date' %}
        {% set the_class = the_class ~ ' datefield' %}
    {% elseif column_type == 'datetime' or column_type starts with 'timestamp' %}
        {% set the_class = the_class ~ ' datetimefield' %}
    {% elseif column_type starts with 'bit' %}
        {% set the_class = the_class ~ ' bit' %}
    {% endif %}
    <input type="text"
        name="criteriaValues[{{ column_index }}]"
        size="40"
        class="{{ the_class }}"
        id="{{ column_id }}{{ column_index }}"
        {% if criteria_values[column_index] is defined %}
           value="{{ criteria_values[column_index] }}"
        {%- endif %} />
{% endif %}
