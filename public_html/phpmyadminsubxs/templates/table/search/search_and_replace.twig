{% trans 'Find:' %}
<input type="text" value="" name="find" required />
{% trans 'Replace with:' %}
<input type="text" value="" name="replaceWith" />

{% trans 'Column:' %}
<select name="columnIndex">
    {% for i in 0..column_names|length - 1 %}
        {% set type = preg_replace('@\\(.*@s', '', column_types[i]) %}
        {% if sql_types.getTypeClass(type) == 'CHAR' %}
            {% set column = column_names[i] %}
            <option value="{{ i }}">
                {{ column }}
            </option>
        {% endif %}
    {% endfor %}
</select>

{% include 'checkbox.twig' with {
    'html_field_id': 'useRegex',
    'html_field_name': 'useRegex',
    'label': 'Use regular expression'|trans,
    'checked': false,
    'onclick': false
} only %}
