{# Get already set search criteria (if any) #}
{% set type = [] %}
{% set collation = [] %}
{% set func = [] %}
{% set value = [] %}

{% for i in 0..3 %}
    {# After X-Axis and Y-Axis column rows, display additional criteria option #}
    {% if i == 2 %}
        <tr>
            <td>
                {% trans 'Additional search criteria' %}
            </td>
        </tr>
    {% endif %}
    <tr class="noclick">
        <th>
            <select name="criteriaColumnNames[]" id="tableid_{{ i }}" >
                <option value="pma_null">
                    {% trans 'None' %}
                </option>
                {% for j in 0..column_names|length - 1 %}
                    {% if criteria_column_names[i] is defined
                        and criteria_column_names[i] == column_names[j] %}
                        <option value="{{ column_names[j] }}" selected="selected">
                            {{ column_names[j] }}
                        </option>
                    {% else %}
                        <option value="{{ column_names[j] }}">
                            {{ column_names[j] }}
                        </option>
                    {% endif %}
                {% endfor %}
            </select>
        </th>
        {% if criteria_column_names is defined
            and criteria_column_names[i] != 'pma_null' %}
            {% set key = array_search(criteria_column_names[i], column_names) %}
            {% set properties = self.getColumnProperties(i, key) %}
            {% set type = type|merge({i: properties['type']}) %}
            {% set collation = collation|merge({i: properties['collation']}) %}
            {% set func = func|merge({i: properties['func']}) %}
            {% set value = value|merge({i: properties['value']}) %}
        {% endif %}
        {# Column type #}
        <td dir="ltr">
            {{ type[i] is defined ? type[i] }}
        </td>
        {# Column Collation #}
        <td>
            {{ collation[i] is defined ? collation[i] }}
        </td>
        {# Select options for column operators #}
        <td>
            {{ func[i] is defined ? func[i]|raw }}
        </td>
        {# Inputbox for search criteria value #}
        <td>
        </td>
        <td>
            {{ value[i] is defined ? value[i]|raw }}
            {# Displays hidden fields #}
            <input type="hidden"
                name="criteriaColumnTypes[{{ i }}]"
                id="types_{{ i }}"
                {%- if criteria_column_types[i] is defined %}
                    value="{{ criteria_column_types[i] }}"
                {%- endif %} />
            <input type="hidden"
                name="criteriaColumnCollations[{{ i }}]"
                id="collations_{{ i }}" />
        </td>
    </tr>
{% endfor %}
