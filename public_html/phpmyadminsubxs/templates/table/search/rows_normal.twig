{% for column_index in 0..column_names|length - 1 %}
    <tr class="noclick">
        {# If 'Function' column is present trying to change comment #}
        {% if geom_column_flag %}
            {% include 'table/search/geom_func.twig' with {
                'column_index': column_index,
                'column_types': column_types
            } only %}
        {% endif %}
        {# Displays column's name, type, collation and value #}
        <th>
            {{ column_names[column_index] }}
        </th>
        {% set properties = self.getColumnProperties(column_index, column_index) %}
        <td dir="ltr">
            {{ properties['type'] }}
        </td>
        <td>
            {{ properties['collation'] }}
        </td>
        <td>
            {{ properties['func']|raw }}
        </td>
        {# here, the data-type attribute is needed for a date/time picker #}
        <td data-type="{{ properties['type'] }}">
            {{ properties['value']|raw }}
            {# Displays hidden fields #}
            <input type="hidden"
                name="criteriaColumnNames[{{ column_index }}]"
                value="{{ column_names[column_index] }}" />
            <input type="hidden"
                name="criteriaColumnTypes[{{ column_index }}]"
                value="{{ column_types[column_index] }}" />
            <input type="hidden"
                name="criteriaColumnCollations[{{ column_index }}]"
                value="{{ column_collations[column_index] }}" />
        </td>
    </tr>
{% endfor %}
