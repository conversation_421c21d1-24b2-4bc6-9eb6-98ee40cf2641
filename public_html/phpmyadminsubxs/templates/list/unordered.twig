<ul{% if id is not empty %} id="{{ id }}"{% endif -%}
    {%- if class is not empty %} class="{{ class }}"{% endif %}>

    {% if items is not empty %}
        {% for item in items %}
            {% if item is not iterable %}
                {% set item = {'content': item}  %}
            {% endif %}
            {% include 'list/item.twig' with item only %}
        {% endfor %}
    {% elseif content is not empty %}
        {{ content|raw }}
    {% endif %}
</ul>
