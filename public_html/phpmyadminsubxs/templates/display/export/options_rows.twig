<div class="exportoptions" id="rows">
    <h3>{% trans 'Rows:' %}</h3>
    <ul>
        <li>
            <input type="radio" name="allrows" value="0" id="radio_allrows_0"
                {{- allrows is not null and allrows == 0 ? ' checked' }}>
            <label for="radio_allrows_0">{% trans 'Dump some row(s)' %}</label>
            <ul>
                <li>
                    <label for="limit_to">{% trans 'Number of rows:' %}</label>
                    <input type="text" id="limit_to" name="limit_to" size="5" value="
                        {%- spaceless %}
                            {% if limit_to is not null %}
                                {{ limit_to }}
                            {% elseif unlim_num_rows is not empty and unlim_num_rows != 0 %}
                                {{ unlim_num_rows }}
                            {% else %}
                                {{ number_of_rows }}
                            {% endif %}
                        {% endspaceless %}" onfocus="this.select()">
                </li>
                <li>
                    <label for="limit_from">{% trans 'Row to begin at:' %}</label>
                    <input type="text" id="limit_from" name="limit_from" size="5" value="
                        {{- limit_from is not null ? limit_from : 0 }}" onfocus="this.select()">
                </li>
            </ul>
        </li>
        <li>
            <input type="radio" name="allrows" value="1" id="radio_allrows_1"
                {{- allrows is null or allrows == 1 ? ' checked' }}>
             <label for="radio_allrows_1">{% trans 'Dump all rows' %}</label>
        </li>
    </ul>
</div>
