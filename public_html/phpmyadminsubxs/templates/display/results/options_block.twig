<form method="post" action="sql.php" name="displayOptionsForm" class="ajax print_ignore">
    {{ Url_getHiddenInputs({
        'db': db,
        'table': table,
        'sql_query': sql_query,
        'goto': goto,
        'display_options_form': 1
    }) }}

    {{ Util_getDivForSliderEffect('', 'Options'|trans) }}
        <fieldset>
            <div class="formelement">
                {# pftext means "partial or full texts" (done to reduce line lengths #}
                {{ Util_getRadioFields(
                    'pftext',
                    {
                        'P': 'Partial texts'|trans,
                        'F': 'Full texts'|trans
                    },
                    pftext,
                    true,
                    true,
                    '',
                    'pftext_' ~ unique_id
                ) }}
            </div>

            {% if relwork and displaywork %}
                <div class="formelement">
                    {{ Util_getRadioFields(
                        'relational_display',
                        {
                            'K': 'Relational key'|trans,
                            'D': 'Display column for relationships'|trans
                        },
                        relational_display,
                        true,
                        true,
                        '',
                        'relational_display_' ~ unique_id
                    ) }}
                </div>
            {% endif %}

            <div class="formelement">
                {% include 'checkbox.twig' with {
                    'html_field_name': 'display_binary',
                    'label': 'Show binary contents'|trans,
                    'checked': display_binary is not empty,
                    'onclick': false,
                    'html_field_id': 'display_binary_' ~ unique_id
                } only %}
                {% include 'checkbox.twig' with {
                    'html_field_name': 'display_blob',
                    'label': 'Show BLOB contents'|trans,
                    'checked': display_blob is not empty,
                    'onclick': false,
                    'html_field_id': 'display_blob_' ~ unique_id
                } only %}
            </div>

            {# I would have preferred to name this "display_transformation".
               This is the only way I found to be able to keep this setting sticky
               per SQL query, and at the same time have a default that displays
               the transformations. #}
            <div class="formelement">
                {% include 'checkbox.twig' with {
                    'html_field_name': 'hide_transformation',
                    'label': 'Hide browser transformation'|trans,
                    'checked': hide_transformation is not empty,
                    'onclick': false,
                    'html_field_id': 'hide_transformation_' ~ unique_id
                } only %}
            </div>


            {% if possible_as_geometry %}
                <div class="formelement">
                    {{ Util_getRadioFields(
                        'geoOption',
                        {
                            'GEOM': 'Geometry'|trans,
                            'WKT': 'Well Known Text'|trans,
                            'WKB': 'Well Known Binary'|trans
                        },
                        geo_option,
                        true,
                        true,
                        '',
                        'geoOption_' ~ unique_id
                    ) }}
                </div>
            {% else %}
                <div class="formelement">
                    {{ possible_as_geometry }}
                    {{ Util_getRadioFields(
                        'geoOption',
                        {
                            'WKT': 'Well Known Text'|trans,
                            'WKB': 'Well Known Binary'|trans
                        },
                        geo_option,
                        true,
                        true,
                        '',
                        'geoOption_' ~ unique_id
                    ) }}
                </div>
            {% endif %}
            <div class="clearfloat"></div>
        </fieldset>

        <fieldset class="tblFooters">
            <input type="submit" value="{% trans 'Go' %}" />
        </fieldset>
    </div>{# slider effect div #}
</form>
