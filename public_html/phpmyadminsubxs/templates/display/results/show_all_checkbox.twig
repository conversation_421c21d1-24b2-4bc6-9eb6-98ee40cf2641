<td>
    <form action="sql.php" method="post">
        {{ Url_getHiddenInputs(db, table) }}
        <input type="hidden" name="sql_query" value="{{ html_sql_query|raw }}" />
        <input type="hidden" name="pos" value="0" />
        <input type="hidden" name="is_browse_distinct" value="{{ is_browse_distinct }}" />
        <input type="hidden" name="session_max_rows" value="{{ not showing_all ? 'all' : max_rows }}" />
        <input type="hidden" name="goto" value="{{ goto }}" />
        <input type="checkbox" name="navig" id="showAll_{{ unique_id }}" class="showAllRows"
            {{- showing_all ? ' checked="checked"' }} value="all" />
        <label for="showAll_{{ unique_id }}">{% trans 'Show all' %}</label>
    </form>
</td>
