<h3>{% trans 'Untracked tables' %}</h3>
<form method="post" action="db_tracking.php" name="untrackedForm"
    id="untrackedForm" class="ajax">
    {{ Url_getHiddenInputs(db) }}
    <table id="noversions" class="data">
        <thead>
            <tr>
                <th></th>
                <th>{% trans 'Table' %}</th>
                <th>{% trans 'Action' %}</th>
            </tr>
        </thead>
        <tbody>
            {% for table_name in untracked_tables if Tracker_getVersion(db, table_name) == -1 %}
                <tr>
                    <td class="center">
                        <input type="checkbox" name="selected_tbl[]"
                            class="checkall" id="selected_tbl_{{ table_name }}"
                            value="{{ table_name }}"/>
                    </td>
                    <th>
                        <label for="selected_tbl_{{ table_name }}">
                            {{ table_name }}
                        </label>
                    </th>
                    <td>
                        <a href="tbl_tracking.php{{ url_query|raw }}&amp;table={{ table_name }}">
                            {{ Util_getIcon('eye', 'Track table'|trans) }}
                        </a>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
    {% include 'select_all.twig' with {
        'pma_theme_image': pma_theme_image,
        'text_dir': text_dir,
        'form_name': 'untrackedForm'
    } only %}
    {{ Util_getButtonOrImage(
        'submit_mult',
        'mult_submit',
        'Track table'|trans,
        'eye',
        'track'
    ) }}
</form>
