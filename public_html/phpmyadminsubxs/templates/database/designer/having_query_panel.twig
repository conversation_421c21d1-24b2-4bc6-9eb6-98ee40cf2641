<table id="query_having" class="hide" width="5%" cellpadding="0" cellspacing="0">
    <tbody>
        <tr>
            <td class="frams1" width="10px">
            </td>
            <td class="frams5" width="99%" >
            </td>
            <td class="frams2" width="10px">
                <div class="bor">
                </div>
            </td>
        </tr>
        <tr>
            <td class="frams8">
            </td>
            <td class="input_tab">
                <table width="168" class="center" cellpadding="2" cellspacing="0">
                    <thead>
                        <tr>
                            <td colspan="2" class="center nowrap">
                                <strong>
                                    HAVING
                                </strong>
                            </td>
                        </tr>
                    </thead>
                    <tbody id="rename_to">
                        <tr>
                            <td width="58" class="nowrap">
                                {% trans 'Operator' %}
                            </td>
                            <td width="102">
                                <select name="hoperator" id="hoperator">
                                    <option value="---" selected="selected">
                                        ---
                                    </option>
                                    <option value="None" >
                                        None
                                    </option>
                                    <option value="sum" >
                                        SUM
                                    </option>
                                    <option value="min">
                                        MIN
                                    </option>
                                    <option value="max">
                                        MAX
                                    </option>
                                    <option value="avg">
                                        AVG
                                    </option>
                                    <option value="count">
                                        COUNT
                                    </option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <tr>
                                <td width="58" class="nowrap">
                                    {% trans 'Operator' %}
                                </td>
                                <td width="102">
                                    <select name="hrel_opt" id="hrel_opt">
                                        <option value="--" selected="selected">
                                            --
                                        </option>
                                        <option value="=">
                                            =
                                        </option>
                                        <option value="&gt;">
                                            &gt;
                                        </option>
                                        <option value="&lt;">
                                            &lt;
                                        </option>
                                        <option value="&gt;=">
                                            &gt;=
                                        </option>
                                        <option value="&lt;=">
                                            &lt;=
                                        </option>
                                        <option value="NOT">
                                            NOT
                                        </option>
                                        <option value="IN">
                                            IN
                                        </option>
                                        <option value="EXCEPT">
                                            {% trans 'Except' %}
                                        </option>
                                        <option value="NOT IN">
                                            NOT IN
                                        </option>
                                    </select>
                                </td>
                        </tr>
                        <tr>
                            <td class="nowrap">
                                {% trans 'Value' %}
                                <br />
                                {% trans 'subquery' %}
                            </td>
                            <td>
                                <textarea id="hQuery" cols="18">
                                </textarea>
                            </td>
                        </tr>
                    </tbody>
                    <tbody>
                        <tr>
                            <td colspan="2" class="center nowrap">
                                <input type="button" id="ok_edit_having" class="butt"
                                    name="Button" value="{% trans 'OK' %}" />
                                <input id="query_having_button" type="button"
                                    class="butt"
                                    name="Button"
                                    value="{% trans 'Cancel' %}" />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td class="frams6">
            </td>
        </tr>
        <tr>
            <td class="frams4">
                <div class="bor">
                </div>
            </td>
            <td class="frams7">
            </td>
            <td class="frams3">
            </td>
        </tr>
    </tbody>
</table>
