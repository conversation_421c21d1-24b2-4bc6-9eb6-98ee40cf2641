{% if not visual_builder %}
    <div id="name-panel">
        <span id="page_name">
            {{ selected_page == null ? 'Untitled'|trans : selected_page }}
        </span>
        <span id="saved_state">
            {{ selected_page == null ? '*' : '' }}
        </span>
    </div>
{% endif %}
<div class="designer_header side-menu" id="side_menu">
    <a class="M_butt" id="key_Show_left_menu" href="#">
        <img title="{% trans 'Show/Hide tables list' %}"
             alt="v"
             src="{{ theme.getImgPath('designer/downarrow2_m.png') }}"
             data-down="{{ theme.getImgPath('designer/downarrow2_m.png') }}"
             data-up="{{ theme.getImgPath('designer/uparrow2_m.png') }}" />
        <span class="hide hidable">
            {% trans 'Show/Hide tables list' %}
        </span>
    </a>
    <a href="#" id="toggleFullscreen" class="M_butt">
        <img title="{% trans 'View in fullscreen' %}"
             src="{{ theme.getImgPath('designer/viewInFullscreen.png') }}"
             data-enter="{{ theme.getImgPath('designer/viewInFullscreen.png') }}"
             data-exit="{{ theme.getImgPath('designer/exitFullscreen.png') }}" />
        <span class="hide hidable"
              data-exit="{% trans 'Exit fullscreen' %}"
              data-enter="{% trans 'View in fullscreen' %}">
            {% trans 'View in fullscreen' %}
        </span>
    </a>
    <a href="#" id="addOtherDbTables" class="M_butt">
        <img title="{% trans 'Add tables from other databases' %}"
             src="{{ theme.getImgPath('designer/other_table.png') }}"/>
        <span class="hide hidable">
            {% trans 'Add tables from other databases' %}
        </span>
    </a>
    {% if not visual_builder %}
        <a id="newPage" href="#" class="M_butt">
            <img title="{% trans 'New page' %}"
                 alt=""
                 src="{{ theme.getImgPath('designer/page_add.png') }}" />
            <span class="hide hidable">
                {% trans 'New page' %}
            </span>
        </a>
        <a href="#" id="editPage" class="M_butt ajax">
            <img title="{% trans 'Open page' %}"
                 src="{{ theme.getImgPath('designer/page_edit.png') }}" />
            <span class="hide hidable">
                {% trans 'Open page' %}
            </span>
        </a>
        <a href="#" id="savePos" class="M_butt">
            <img title="{% trans 'Save page' %}"
                 src="{{ theme.getImgPath('designer/save.png') }}" />
            <span class="hide hidable">
                {% trans 'Save page' %}
            </span>
        </a>
        <a href="#" id="SaveAs" class="M_butt ajax">
            <img title="{% trans 'Save page as' %}"
                 src="{{ theme.getImgPath('designer/save_as.png') }}" />
            <span class="hide hidable">
                {% trans 'Save page as' %}
            </span>
        </a>
        <a href="#" id="delPages" class="M_butt ajax">
            <img title="{% trans 'Delete pages' %}"
                 src="{{ theme.getImgPath('designer/page_delete.png') }}" />
            <span class="hide hidable">
                {% trans 'Delete pages' %}
            </span>
        </a>
        <a href="#" id="StartTableNew" class="M_butt">
            <img title="{% trans 'Create table' %}"
                 src="{{ theme.getImgPath('designer/table.png') }}" />
            <span class="hide hidable">
                {% trans 'Create table' %}
            </span>
        </a>
        <a href="#" class="M_butt" id="rel_button">
            <img title="{% trans 'Create relationship' %}"
                 src="{{ theme.getImgPath('designer/relation.png') }}" />
            <span class="hide hidable">
                {% trans 'Create relationship' %}
            </span>
        </a>
        <a href="#" class="M_butt" id="display_field_button">
            <img title="{% trans 'Choose column to display' %}"
                 src="{{ theme.getImgPath('designer/display_field.png') }}" />
            <span class="hide hidable">
                {% trans 'Choose column to display' %}
            </span>
        </a>
        <a href="#" id="reloadPage" class="M_butt">
            <img title="{% trans 'Reload' %}"
                 src="{{ theme.getImgPath('designer/reload.png') }}" />
            <span class="hide hidable">
                {% trans 'Reload' %}
            </span>
        </a>
        <a href="{{ Util_getDocuLink('faq', 'faq6-31') }}"
           target="documentation"
           class="M_butt">
            <img title="{% trans 'Help' %}"
                 src="{{ theme.getImgPath('designer/help.png') }}" />
            <span class="hide hidable">
                {% trans 'Help' %}
            </span>
        </a>
    {% endif %}
    <a href="#" class="{{ params_array['angular_direct'] }}" id="angular_direct_button">
        <img title="{% trans 'Angular links' %} / {% trans 'Direct links' %}"
             src="{{ theme.getImgPath('designer/ang_direct.png') }}" />
        <span class="hide hidable">
            {% trans 'Angular links' %} / {% trans 'Direct links' %}
        </span>
    </a>
    <a href="#" class="{{ params_array['snap_to_grid'] }}" id="grid_button">
        <img title="{% trans 'Snap to grid' %}" src="{{ theme.getImgPath('designer/grid.png') }}" />
        <span class="hide hidable">
            {% trans 'Snap to grid' %}
        </span>
    </a>
    <a href="#" class="{{ params_array['small_big_all'] }}" id="key_SB_all">
        <img title="{% trans 'Small/Big All' %}"
             alt="v"
             src="{{ theme.getImgPath('designer/downarrow1.png') }}"
             data-down="{{ theme.getImgPath('designer/downarrow1.png') }}"
             data-right="{{ theme.getImgPath('designer/rightarrow1.png') }}" />
        <span class="hide hidable">
            {% trans 'Small/Big All' %}
        </span>
    </a>
    <a href="#" id="SmallTabInvert" class="M_butt">
        <img title="{% trans 'Toggle small/big' %}"
             src="{{ theme.getImgPath('designer/bottom.png') }}" />
        <span class="hide hidable">
            {% trans 'Toggle small/big' %}
        </span>
    </a>
    <a href="#" id="relLineInvert" class="{{ params_array['relation_lines'] }}" >
        <img title="{% trans 'Toggle relationship lines' %}"
             src="{{ theme.getImgPath('designer/toggle_lines.png') }}" />
        <span class="hide hidable">
            {% trans 'Toggle relationship lines' %}
        </span>
    </a>
    {% if not visual_builder %}
        <a href="#" id="exportPages" class="M_butt" >
            <img title="{% trans 'Export schema' %}"
                 src="{{ theme.getImgPath('designer/export.png') }}" />
            <span class="hide hidable">
                {% trans 'Export schema' %}
            </span>
        </a>
    {% else %}
        <a id="build_query_button"
           class="M_butt"
           href="#"
           class="M_butt">
            <img title="{% trans 'Build Query' %}"
                 src="{{ theme.getImgPath('designer/query_builder.png') }}" />
            <span class="hide hidable">
                {% trans 'Build Query' %}
            </span>
        </a>
    {% endif %}
    <a href="#" class="{{ params_array['side_menu'] }}" id="key_Left_Right">
        <img title="{% trans 'Move Menu' %}" alt=">"
             data-right="{{ theme.getImgPath('designer/2leftarrow_m.png') }}"
             src="{{ theme.getImgPath('designer/2rightarrow_m.png') }}" />
        <span class="hide hidable">
            {% trans 'Move Menu' %}
        </span>
    </a>
    <a href="#" class="{{ params_array['pin_text'] }}" id="pin_Text">
        <img title="{% trans 'Pin text' %}"
             alt=">"
             data-right="{{ theme.getImgPath('designer/anchor.png') }}"
             src="{{ theme.getImgPath('designer/anchor.png') }}" />
        <span class="hide hidable">
            {% trans 'Pin text' %}
        </span>
    </a>
</div>
